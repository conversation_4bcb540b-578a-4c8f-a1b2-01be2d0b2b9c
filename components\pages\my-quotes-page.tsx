"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { FileText, Search, Eye, Download, Share, Calendar, DollarSign, Clock, ArrowLeft, Plus } from "lucide-react"
import Link from "next/link"

interface Quote {
  id: string
  title: string
  date: string
  validUntil: string
  status: "draft" | "sent" | "viewed" | "accepted" | "expired" | "declined"
  total: number
  items: QuoteItem[]
  notes: string
}

interface QuoteItem {
  id: string
  name: string
  category: string
  quantity: number
  unitPrice: number
  total: number
  specifications: string[]
}

export default function MyQuotesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("date-desc")

  const quotes: Quote[] = [
    {
      id: "Q-2024-001",
      title: "Residential Window Replacement - Main Floor",
      date: "2024-01-20",
      validUntil: "2024-02-20",
      status: "sent",
      total: 2450.0,
      notes: "Customer requested energy-efficient options with white frames",
      items: [
        {
          id: "1",
          name: "Double Hung Window",
          category: "Windows",
          quantity: 4,
          unitPrice: 299.0,
          total: 1196.0,
          specifications: ["White Frame", "Low-E Glass", "Standard Screen"],
        },
        {
          id: "2",
          name: "Casement Window",
          category: "Windows",
          quantity: 2,
          unitPrice: 329.0,
          total: 658.0,
          specifications: ["White Frame", "Triple Pane", "FlexScreen"],
        },
        {
          id: "3",
          name: "Installation Service",
          category: "Services",
          quantity: 1,
          unitPrice: 596.0,
          total: 596.0,
          specifications: ["Professional Installation", "Cleanup Included"],
        },
      ],
    },
    {
      id: "Q-2024-002",
      title: "Commercial Office Building - Floor 3",
      date: "2024-01-18",
      validUntil: "2024-02-18",
      status: "viewed",
      total: 890.0,
      notes: "Bulk order for office renovation project",
      items: [
        {
          id: "1",
          name: "Fixed Window",
          category: "Windows",
          quantity: 6,
          unitPrice: 149.0,
          total: 894.0,
          specifications: ["Bronze Frame", "Clear Glass", "No Screen"],
        },
      ],
    },
    {
      id: "Q-2024-003",
      title: "Kitchen Renovation - Bay Window",
      date: "2024-01-15",
      validUntil: "2024-02-15",
      status: "accepted",
      total: 1650.0,
      notes: "Custom bay window for kitchen remodel",
      items: [
        {
          id: "1",
          name: "Bay Window Assembly",
          category: "Windows",
          quantity: 1,
          unitPrice: 1299.0,
          total: 1299.0,
          specifications: ["White Frame", "Low-E Glass", "Custom Size"],
        },
        {
          id: "2",
          name: "Installation Service",
          category: "Services",
          quantity: 1,
          unitPrice: 351.0,
          total: 351.0,
          specifications: ["Custom Installation", "Structural Support"],
        },
      ],
    },
    {
      id: "Q-2024-004",
      title: "Basement Windows Replacement",
      date: "2024-01-10",
      validUntil: "2024-01-25",
      status: "expired",
      total: 567.0,
      notes: "Egress windows for basement bedroom",
      items: [
        {
          id: "1",
          name: "Egress Window",
          category: "Windows",
          quantity: 2,
          unitPrice: 199.0,
          total: 398.0,
          specifications: ["White Frame", "Tempered Glass", "Security Screen"],
        },
        {
          id: "2",
          name: "Window Well",
          category: "Accessories",
          quantity: 2,
          unitPrice: 84.5,
          total: 169.0,
          specifications: ["Galvanized Steel", "Drain System"],
        },
      ],
    },
    {
      id: "Q-2024-005",
      title: "Patio Door Replacement",
      date: "2024-01-05",
      validUntil: "2024-01-20",
      status: "declined",
      total: 1299.0,
      notes: "Customer decided to postpone project",
      items: [
        {
          id: "1",
          name: "Sliding Patio Door",
          category: "Doors",
          quantity: 1,
          unitPrice: 899.0,
          total: 899.0,
          specifications: ["Bronze Frame", "Low-E Glass", "Screen Door"],
        },
        {
          id: "2",
          name: "Installation Service",
          category: "Services",
          quantity: 1,
          unitPrice: 400.0,
          total: 400.0,
          specifications: ["Professional Installation", "Old Door Removal"],
        },
      ],
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "accepted":
        return "bg-green-100 text-green-800"
      case "sent":
        return "bg-blue-100 text-blue-800"
      case "viewed":
        return "bg-yellow-100 text-yellow-800"
      case "draft":
        return "bg-gray-100 text-gray-800"
      case "expired":
      case "declined":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "accepted":
        return "✓"
      case "sent":
        return "→"
      case "viewed":
        return "👁"
      case "draft":
        return "📝"
      case "expired":
        return "⏰"
      case "declined":
        return "✗"
      default:
        return "?"
    }
  }

  const filteredQuotes = quotes.filter((quote) => {
    const matchesSearch =
      quote.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quote.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || quote.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const sortedQuotes = [...filteredQuotes].sort((a, b) => {
    switch (sortBy) {
      case "date-desc":
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      case "date-asc":
        return new Date(a.date).getTime() - new Date(b.date).getTime()
      case "total-desc":
        return b.total - a.total
      case "total-asc":
        return a.total - b.total
      case "status":
        return a.status.localeCompare(b.status)
      default:
        return 0
    }
  })

  const totalQuotes = quotes.length
  const activeQuotes = quotes.filter((q) => ["sent", "viewed"].includes(q.status)).length
  const acceptedQuotes = quotes.filter((q) => q.status === "accepted").length
  const totalValue = quotes.reduce((sum, quote) => sum + quote.total, 0)

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/profile">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Profile
              </Button>
            </Link>
          </div>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="font-bebas text-4xl text-gray-900 mb-2">My Quotes</h1>
              <p className="text-gray-600">Manage and track your window quotes</p>
            </div>
            <Link href="/instant-quote">
              <Button className="bg-primary-orange hover:bg-orange-600 text-white">
                <Plus className="w-4 h-4 mr-2" />
                New Quote
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{totalQuotes}</div>
              <div className="text-sm text-gray-600">Total Quotes</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{activeQuotes}</div>
              <div className="text-sm text-gray-600">Active Quotes</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{acceptedQuotes}</div>
              <div className="text-sm text-gray-600">Accepted</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">${totalValue.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Total Value</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search quotes by title or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="sent">Sent</SelectItem>
                    <SelectItem value="viewed">Viewed</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="declined">Declined</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest First</SelectItem>
                    <SelectItem value="date-asc">Oldest First</SelectItem>
                    <SelectItem value="total-desc">Highest Value</SelectItem>
                    <SelectItem value="total-asc">Lowest Value</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quotes List */}
        <div className="space-y-6">
          {sortedQuotes.map((quote) => (
            <Card key={quote.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center gap-4 mb-4 md:mb-0">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                      <FileText className="w-6 h-6 text-gray-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{quote.title}</CardTitle>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {new Date(quote.date).toLocaleDateString()}
                        </span>
                        <span>Quote ID: {quote.id}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={`${getStatusColor(quote.status)} flex items-center gap-1`}>
                      <span>{getStatusIcon(quote.status)}</span>
                      {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                    </Badge>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary-orange">${quote.total.toFixed(2)}</div>
                      <div className="text-sm text-gray-600">
                        Valid until {new Date(quote.validUntil).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Quote Items Summary */}
                  <div>
                    <h4 className="font-semibold mb-2">Items ({quote.items.length})</h4>
                    <div className="space-y-2">
                      {quote.items.map((item) => (
                        <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-gray-600">
                              Qty: {item.quantity} × ${item.unitPrice.toFixed(2)}
                            </div>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {item.specifications.map((spec, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {spec}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">${item.total.toFixed(2)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Notes */}
                  {quote.notes && (
                    <div>
                      <h4 className="font-semibold mb-2">Notes</h4>
                      <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">{quote.notes}</p>
                    </div>
                  )}

                  <Separator />

                  {/* Actions */}
                  <div className="flex flex-wrap gap-3">
                    <Link href={`/profile/quotes/${quote.id}`}>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                      >
                        <Eye className="w-4 h-4" />
                        View Details
                      </Button>
                    </Link>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                      onClick={() => {
                        // Create and download PDF
                        const element = document.createElement("a")
                        element.setAttribute(
                          "href",
                          "data:text/plain;charset=utf-8," +
                            encodeURIComponent(`Quote ${quote.id}\n${quote.title}\nTotal: $${quote.total.toFixed(2)}`),
                        )
                        element.setAttribute("download", `quote-${quote.id}.txt`)
                        element.style.display = "none"
                        document.body.appendChild(element)
                        element.click()
                        document.body.removeChild(element)
                      }}
                    >
                      <Download className="w-4 h-4" />
                      Download PDF
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                      onClick={() => {
                        if (navigator.share) {
                          navigator.share({
                            title: quote.title,
                            text: `Quote ${quote.id}: ${quote.title}`,
                            url: window.location.href,
                          })
                        } else {
                          navigator.clipboard.writeText(
                            `${quote.title} - Quote ${quote.id}: $${quote.total.toFixed(2)}`,
                          )
                          alert("Quote details copied to clipboard!")
                        }
                      }}
                    >
                      <Share className="w-4 h-4" />
                      Share Quote
                    </Button>
                    {quote.status === "sent" || quote.status === "viewed" ? (
                      <Button size="sm" className="bg-primary-orange hover:bg-orange-600 text-white">
                        Accept Quote
                      </Button>
                    ) : quote.status === "draft" ? (
                      <Button size="sm" className="bg-primary-orange hover:bg-orange-600 text-white">
                        Send Quote
                      </Button>
                    ) : null}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {sortedQuotes.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No quotes found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your search or filter criteria"
                  : "You haven't created any quotes yet"}
              </p>
              <Link href="/instant-quote">
                <Button className="bg-primary-orange hover:bg-orange-600 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Quote
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </main>
  )
}
