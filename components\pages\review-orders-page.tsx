"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import {
  Package,
  Search,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  Download,
  RefreshCw,
  ArrowLeft,
  Calendar,
  DollarSign,
  ShoppingBag,
} from "lucide-react"
import Link from "next/link"

interface OrderItem {
  id: string
  name: string
  category: string
  quantity: number
  unitPrice: number
  total: number
  specifications: string[]
  image: string
}

interface Order {
  id: string
  date: string
  status: "pending" | "confirmed" | "processing" | "shipped" | "delivered" | "cancelled"
  total: number
  items: OrderItem[]
  shippingAddress: {
    name: string
    address: string
    city: string
    state: string
    zipCode: string
  }
  tracking?: {
    number: string
    carrier: string
    estimatedDelivery: string
    currentLocation: string
    progress: number
  }
  timeline: {
    status: string
    date: string
    description: string
    completed: boolean
  }[]
}

export default function ReviewOrdersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("date-desc")

  const orders: Order[] = [
    {
      id: "WO-2024-001",
      date: "2024-01-15",
      status: "delivered",
      total: 598.0,
      items: [
        {
          id: "1",
          name: "Double Hung Window",
          category: "Windows",
          quantity: 2,
          unitPrice: 299.0,
          total: 598.0,
          specifications: ["White Frame", "Low-E Glass", "Standard Screen"],
          image: "/placeholder.svg?height=80&width=80",
        },
      ],
      shippingAddress: {
        name: "John Doe",
        address: "123 Main Street",
        city: "New York",
        state: "NY",
        zipCode: "10001",
      },
      tracking: {
        number: "TRK123456789",
        carrier: "FedEx",
        estimatedDelivery: "2024-01-18",
        currentLocation: "Delivered",
        progress: 100,
      },
      timeline: [
        {
          status: "Order Placed",
          date: "2024-01-15",
          description: "Order confirmed and payment processed",
          completed: true,
        },
        { status: "Processing", date: "2024-01-16", description: "Order being prepared for shipment", completed: true },
        { status: "Shipped", date: "2024-01-17", description: "Package shipped via FedEx", completed: true },
        { status: "Delivered", date: "2024-01-18", description: "Package delivered successfully", completed: true },
      ],
    },
    {
      id: "WO-2024-002",
      date: "2024-01-10",
      status: "shipped",
      total: 329.0,
      items: [
        {
          id: "1",
          name: "Casement Window",
          category: "Windows",
          quantity: 1,
          unitPrice: 329.0,
          total: 329.0,
          specifications: ["Bronze Frame", "Triple Pane", "FlexScreen"],
          image: "/placeholder.svg?height=80&width=80",
        },
      ],
      shippingAddress: {
        name: "John Doe",
        address: "123 Main Street",
        city: "New York",
        state: "NY",
        zipCode: "10001",
      },
      tracking: {
        number: "TRK987654321",
        carrier: "UPS",
        estimatedDelivery: "2024-01-22",
        currentLocation: "In Transit - Chicago, IL",
        progress: 75,
      },
      timeline: [
        {
          status: "Order Placed",
          date: "2024-01-10",
          description: "Order confirmed and payment processed",
          completed: true,
        },
        { status: "Processing", date: "2024-01-11", description: "Order being prepared for shipment", completed: true },
        { status: "Shipped", date: "2024-01-20", description: "Package shipped via UPS", completed: true },
        { status: "Out for Delivery", date: "2024-01-22", description: "Package out for delivery", completed: false },
      ],
    },
    {
      id: "WO-2024-003",
      date: "2024-01-05",
      status: "processing",
      total: 1299.0,
      items: [
        {
          id: "1",
          name: "Sliding Patio Door",
          category: "Doors",
          quantity: 1,
          unitPrice: 899.0,
          total: 899.0,
          specifications: ["Bronze Frame", "Low-E Glass", "Screen Door"],
          image: "/placeholder.svg?height=80&width=80",
        },
        {
          id: "2",
          name: "Installation Service",
          category: "Services",
          quantity: 1,
          unitPrice: 400.0,
          total: 400.0,
          specifications: ["Professional Installation", "Old Door Removal"],
          image: "/placeholder.svg?height=80&width=80",
        },
      ],
      shippingAddress: {
        name: "John Doe",
        address: "123 Main Street",
        city: "New York",
        state: "NY",
        zipCode: "10001",
      },
      timeline: [
        {
          status: "Order Placed",
          date: "2024-01-05",
          description: "Order confirmed and payment processed",
          completed: true,
        },
        {
          status: "Processing",
          date: "2024-01-06",
          description: "Order being prepared for shipment",
          completed: false,
        },
        { status: "Shipped", date: "", description: "Package will be shipped soon", completed: false },
        { status: "Delivered", date: "", description: "Estimated delivery date TBD", completed: false },
      ],
    },
    {
      id: "WO-2024-004",
      date: "2024-01-01",
      status: "cancelled",
      total: 567.0,
      items: [
        {
          id: "1",
          name: "Awning Window",
          category: "Windows",
          quantity: 2,
          unitPrice: 279.0,
          total: 558.0,
          specifications: ["White Frame", "Standard Glass", "No Screen"],
          image: "/placeholder.svg?height=80&width=80",
        },
      ],
      shippingAddress: {
        name: "John Doe",
        address: "123 Main Street",
        city: "New York",
        state: "NY",
        zipCode: "10001",
      },
      timeline: [
        {
          status: "Order Placed",
          date: "2024-01-01",
          description: "Order confirmed and payment processed",
          completed: true,
        },
        {
          status: "Cancelled",
          date: "2024-01-02",
          description: "Order cancelled by customer request",
          completed: true,
        },
      ],
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "bg-green-100 text-green-800"
      case "shipped":
        return "bg-blue-100 text-blue-800"
      case "processing":
        return "bg-yellow-100 text-yellow-800"
      case "confirmed":
        return "bg-purple-100 text-purple-800"
      case "pending":
        return "bg-gray-100 text-gray-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "delivered":
        return <CheckCircle className="w-4 h-4" />
      case "shipped":
        return <Truck className="w-4 h-4" />
      case "processing":
        return <RefreshCw className="w-4 h-4" />
      case "confirmed":
        return <CheckCircle className="w-4 h-4" />
      case "pending":
        return <Clock className="w-4 h-4" />
      case "cancelled":
        return <AlertCircle className="w-4 h-4" />
      default:
        return <Package className="w-4 h-4" />
    }
  }

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.items.some((item) => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = statusFilter === "all" || order.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const sortedOrders = [...filteredOrders].sort((a, b) => {
    switch (sortBy) {
      case "date-desc":
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      case "date-asc":
        return new Date(a.date).getTime() - new Date(b.date).getTime()
      case "total-desc":
        return b.total - a.total
      case "total-asc":
        return a.total - b.total
      case "status":
        return a.status.localeCompare(b.status)
      default:
        return 0
    }
  })

  const totalOrders = orders.length
  const activeOrders = orders.filter((o) => ["confirmed", "processing", "shipped"].includes(o.status)).length
  const deliveredOrders = orders.filter((o) => o.status === "delivered").length
  const totalSpent = orders.filter((o) => o.status !== "cancelled").reduce((sum, order) => sum + order.total, 0)

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/profile">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Profile
              </Button>
            </Link>
          </div>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="font-bebas text-4xl text-gray-900 mb-2">Order History</h1>
              <p className="text-gray-600">Track and manage your window orders</p>
            </div>
            <Link href="/shop">
              <Button className="bg-primary-orange hover:bg-orange-600 text-white">
                <ShoppingBag className="w-4 h-4 mr-2" />
                Shop Now
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{totalOrders}</div>
              <div className="text-sm text-gray-600">Total Orders</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{activeOrders}</div>
              <div className="text-sm text-gray-600">Active Orders</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{deliveredOrders}</div>
              <div className="text-sm text-gray-600">Delivered</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-gray-900">${totalSpent.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Total Spent</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search orders by ID or product name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-4">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest First</SelectItem>
                    <SelectItem value="date-asc">Oldest First</SelectItem>
                    <SelectItem value="total-desc">Highest Value</SelectItem>
                    <SelectItem value="total-asc">Lowest Value</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <div className="space-y-6">
          {sortedOrders.map((order) => (
            <Card key={order.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex items-center gap-4 mb-4 md:mb-0">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                      {getStatusIcon(order.status)}
                    </div>
                    <div>
                      <CardTitle className="text-xl">Order {order.id}</CardTitle>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {new Date(order.date).toLocaleDateString()}
                        </span>
                        <span>
                          {order.items.length} item{order.items.length !== 1 ? "s" : ""}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                      {getStatusIcon(order.status)}
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </Badge>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary-orange">${order.total.toFixed(2)}</div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Order Items */}
                  <div>
                    <h4 className="font-semibold mb-3">Items</h4>
                    <div className="space-y-3">
                      {order.items.map((item) => (
                        <div key={item.id} className="flex gap-4 p-3 bg-gray-50 rounded-lg">
                          <img
                            src={item.image || "/placeholder.svg"}
                            alt={item.name}
                            className="w-16 h-16 object-cover rounded"
                          />
                          <div className="flex-1">
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-gray-600">{item.category}</div>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {item.specifications.map((spec, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {spec}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">${item.total.toFixed(2)}</div>
                            <div className="text-sm text-gray-600">
                              Qty: {item.quantity} × ${item.unitPrice.toFixed(2)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tracking Information */}
                  {order.tracking && (
                    <div>
                      <h4 className="font-semibold mb-3">Tracking Information</h4>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="flex justify-between items-center mb-3">
                          <div>
                            <div className="font-medium">Tracking Number: {order.tracking.number}</div>
                            <div className="text-sm text-gray-600">
                              Carrier: {order.tracking.carrier} • Current Location: {order.tracking.currentLocation}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-600">Estimated Delivery</div>
                            <div className="font-medium">
                              {new Date(order.tracking.estimatedDelivery).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                        <Progress value={order.tracking.progress} className="h-2" />
                        <div className="text-sm text-gray-600 mt-2">{order.tracking.progress}% Complete</div>
                      </div>
                    </div>
                  )}

                  {/* Order Timeline */}
                  <div>
                    <h4 className="font-semibold mb-3">Order Timeline</h4>
                    <div className="space-y-3">
                      {order.timeline.map((event, index) => (
                        <div key={index} className="flex gap-4">
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              event.completed ? "bg-green-100 text-green-600" : "bg-gray-100 text-gray-400"
                            }`}
                          >
                            {event.completed ? <CheckCircle className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{event.status}</div>
                            <div className="text-sm text-gray-600">{event.description}</div>
                            {event.date && (
                              <div className="text-xs text-gray-500 mt-1">
                                {new Date(event.date).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div>
                    <h4 className="font-semibold mb-2">Shipping Address</h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="font-medium">{order.shippingAddress.name}</div>
                      <div className="text-sm text-gray-600">
                        {order.shippingAddress.address}
                        <br />
                        {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Actions */}
                  <div className="flex flex-wrap gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                    >
                      <Eye className="w-4 h-4" />
                      View Details
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                    >
                      <Download className="w-4 h-4" />
                      Download Invoice
                    </Button>
                    {order.status === "delivered" && (
                      <Button size="sm" className="bg-primary-orange hover:bg-orange-600 text-white">
                        Reorder Items
                      </Button>
                    )}
                    {(order.status === "pending" || order.status === "confirmed") && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white bg-transparent"
                      >
                        Cancel Order
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {sortedOrders.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No orders found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your search or filter criteria"
                  : "You haven't placed any orders yet"}
              </p>
              <Link href="/shop">
                <Button className="bg-primary-orange hover:bg-orange-600 text-white">
                  <ShoppingBag className="w-4 h-4 mr-2" />
                  Start Shopping
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </main>
  )
}
