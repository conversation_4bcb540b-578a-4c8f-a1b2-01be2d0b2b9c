"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore } from "@/lib/door-configurator-store"

export function DoorPreview() {
  const { doorStyle, doorColor, doorGlass, doorHandle, doorRotationY, doorScale } = useDoorConfiguratorStore()

  const getDoorColor = () => {
    switch (doorColor) {
      case "white":
        return "bg-white"
      case "black":
        return "bg-gray-900"
      case "brown":
        return "bg-amber-800"
      case "beige":
        return "bg-amber-100"
      case "gray":
        return "bg-gray-500"
      default:
        return "bg-white"
    }
  }

  const getGlassColor = () => {
    switch (doorGlass) {
      case "clear":
        return "bg-blue-50 bg-opacity-30"
      case "frosted":
        return "bg-white bg-opacity-70"
      case "decorative":
        return "bg-gradient-to-br from-blue-100 to-purple-100"
      case "energy-efficient":
        return "bg-green-50 bg-opacity-50"
      default:
        return "bg-blue-50 bg-opacity-30"
    }
  }

  const getHandleColor = () => {
    switch (doorHandle) {
      case "premium":
        return "bg-yellow-600"
      case "modern":
        return "bg-gray-600"
      case "classic":
        return "bg-amber-700"
      default:
        return "bg-gray-800"
    }
  }

  const renderDoorFront = () => {
    const doorColorClass = getDoorColor()
    const glassColorClass = getGlassColor()
    const handleColorClass = getHandleColor()

    const baseClasses = `w-64 h-96 ${doorColorClass} border-4 border-gray-400 rounded-lg relative shadow-2xl`

    switch (doorStyle) {
      case "without-door-lite":
        return (
          <div className={baseClasses}>
            <div
              className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-16 ${handleColorClass} rounded-full shadow-md`}
            />
          </div>
        )

      case "1/2-lite":
        return (
          <div className={baseClasses}>
            <div className={`h-1/2 ${glassColorClass} border-b-2 border-gray-400 rounded-t-md`} />
            <div className="h-1/2 relative">
              <div
                className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-12 ${handleColorClass} rounded-full shadow-md`}
              />
            </div>
          </div>
        )

      case "3/4-lite":
        return (
          <div className={baseClasses}>
            <div className={`h-3/4 ${glassColorClass} border-b-2 border-gray-400 rounded-t-md`} />
            <div className="h-1/4 relative">
              <div
                className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-8 ${handleColorClass} rounded-full shadow-md`}
              />
            </div>
          </div>
        )

      case "1/4-lite":
        return (
          <div className={baseClasses}>
            <div className={`h-1/4 ${glassColorClass} border-b-2 border-gray-400 rounded-t-md`} />
            <div className="h-3/4 relative">
              <div
                className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-16 ${handleColorClass} rounded-full shadow-md`}
              />
            </div>
          </div>
        )

      case "full-lite":
        return (
          <div className={`${baseClasses} ${glassColorClass}`}>
            <div
              className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-16 ${handleColorClass} rounded-full shadow-md`}
            />
          </div>
        )

      case "vertical-door-lite":
        return (
          <div className={`${baseClasses} flex`}>
            <div className={`w-1/3 ${glassColorClass} border-r-2 border-gray-400`} />
            <div className="w-2/3 relative">
              <div
                className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-16 ${handleColorClass} rounded-full shadow-md`}
              />
            </div>
          </div>
        )

      case "3-lites":
        return (
          <div className={`${baseClasses} flex flex-col gap-1`}>
            <div className={`flex-1 ${glassColorClass} rounded-t-md`} />
            <div className={`flex-1 ${glassColorClass}`} />
            <div className={`flex-1 ${glassColorClass} rounded-b-md relative`}>
              <div
                className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-2 h-6 ${handleColorClass} rounded-full shadow-md`}
              />
            </div>
          </div>
        )

      case "4-lites":
        return (
          <div className={`${baseClasses} grid grid-cols-2 grid-rows-2 gap-1 p-2`}>
            <div className={`${glassColorClass} rounded`} />
            <div className={`${glassColorClass} rounded`} />
            <div className={`${glassColorClass} rounded relative`}>
              <div
                className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-1 h-4 ${handleColorClass} rounded-full shadow-md`}
              />
            </div>
            <div className={`${glassColorClass} rounded`} />
          </div>
        )

      case "centered-vertical-door-lite":
        return (
          <div className={`${baseClasses} flex justify-center items-center px-8`}>
            <div className={`w-1/2 h-3/4 ${glassColorClass} border-2 border-gray-400 rounded`} />
            <div
              className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-16 ${handleColorClass} rounded-full shadow-md`}
            />
          </div>
        )

      default:
        return (
          <div className={baseClasses}>
            <div
              className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-16 ${handleColorClass} rounded-full shadow-md`}
            />
          </div>
        )
    }
  }

  const renderDoorBack = () => {
    const doorColorClass = getDoorColor()
    const baseClasses = `w-64 h-96 ${doorColorClass} border-4 border-gray-400 rounded-lg relative shadow-2xl`
    return (
      <div className={baseClasses}>
        {/* Simplified back of the door - just a plain panel */}
        <div className="w-full h-full flex items-center justify-center text-gray-400 text-sm">Back of Door</div>
      </div>
    )
  }

  return (
    <div className="flex-1 bg-gray-100 flex items-center justify-center p-8">
      <motion.div
        key={`${doorStyle}-${doorColor}-${doorGlass}-${doorHandle}`}
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          rotateY: doorRotationY,
          scale: doorScale,
        }}
        transition={{ duration: 0.3 }}
        className="relative origin-center"
      >
        {/* Conditionally render front or back based on rotation */}
        {doorRotationY === 0 ? renderDoorFront() : renderDoorBack()}
        {/* Door frame shadow */}
        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-72 h-8 bg-gray-400 opacity-30 rounded-full blur-md" />
      </motion.div>
    </div>
  )
}
