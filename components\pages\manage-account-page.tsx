"use client"

import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { User, CreditCard, Lock, LogOut, Trash2, History, MapPin } from "lucide-react"
import { logout } from "@/lib/auth"

export default function ManageAccountPage() {
  const router = useRouter()

  const handleLogout = () => {
    try {
      logout()
      // The logout function will handle the redirect
    } catch (error) {
      console.error("Logout error:", error)
      // Fallback redirect
      router.push("/")
    }
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="font-bebas text-3xl text-black">Manage Your Account</h1>
        <p className="font-poppins text-gray-600">Update your profile, manage orders, and more.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-primary-orange" /> Profile Information
            </CardTitle>
            <CardDescription>View and update your personal details.</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-sm text-gray-700">Name, email, and contact information.</p>
          </CardContent>
          <div className="p-6 pt-0">
            <Link href="/profile/account">
              <Button variant="outline" className="w-full bg-transparent">
                Edit Profile
              </Button>
            </Link>
          </div>
        </Card>

        {/* My Quotes */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5 text-primary-orange" /> My Quotes
            </CardTitle>
            <CardDescription>Review your saved quotes and configurations.</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-sm text-gray-700">Access all your custom product quotes.</p>
          </CardContent>
          <div className="p-6 pt-0">
            <Link href="/profile/quotes">
              <Button variant="outline" className="w-full bg-transparent">
                View Quotes
              </Button>
            </Link>
          </div>
        </Card>

        {/* Order History */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5 text-primary-orange" /> Order History
            </CardTitle>
            <CardDescription>Track past orders and view details.</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-sm text-gray-700">See all your completed and pending orders.</p>
          </CardContent>
          <div className="p-6 pt-0">
            <Link href="/profile/orders">
              <Button variant="outline" className="w-full bg-transparent">
                View Orders
              </Button>
            </Link>
          </div>
        </Card>

        {/* Payment Methods */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-primary-orange" /> Payment Methods
            </CardTitle>
            <CardDescription>Manage your saved credit cards and billing information.</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-sm text-gray-700">Add, edit, or remove payment methods for faster checkout.</p>
          </CardContent>
          <div className="p-6 pt-0">
            <Link href="/profile/payment-methods">
              <Button variant="outline" className="w-full bg-transparent">
                Manage Payments
              </Button>
            </Link>
          </div>
        </Card>

        {/* Security */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-primary-orange" /> Security
            </CardTitle>
            <CardDescription>Update your password and manage two-factor authentication.</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-sm text-gray-700">Keep your account secure with strong passwords and 2FA.</p>
          </CardContent>
          <div className="p-6 pt-0">
            <Link href="/profile/security">
              <Button variant="outline" className="w-full bg-transparent">
                Security Settings
              </Button>
            </Link>
          </div>
        </Card>

        {/* Saved Addresses */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-primary-orange" /> Saved Addresses
            </CardTitle>
            <CardDescription>Manage your shipping and billing addresses.</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow">
            <p className="text-sm text-gray-700">Store multiple addresses for quick and easy checkout.</p>
          </CardContent>
          <div className="p-6 pt-0">
            <Button variant="outline" className="w-full bg-transparent">
              Manage Addresses
            </Button>
          </div>
        </Card>
      </div>

      <Separator className="my-8" />

      {/* Danger Zone */}
      <Card className="border-red-300 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <Trash2 className="h-5 w-5" /> Danger Zone
          </CardTitle>
          <CardDescription className="text-red-600">Actions that will permanently affect your account.</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="font-semibold text-red-700">Delete Account</h3>
            <p className="text-sm text-red-600">Permanently delete your account and all associated data.</p>
          </div>
          <Link href="/profile/delete-account">
            <Button variant="destructive">Delete Account</Button>
          </Link>
        </CardContent>
      </Card>

      <div className="mt-8 text-center">
        <Button onClick={handleLogout} variant="ghost" className="text-red-600 hover:text-red-700">
          <LogOut className="mr-2 h-4 w-4" />
          Sign Out
        </Button>
      </div>
    </div>
  )
}
