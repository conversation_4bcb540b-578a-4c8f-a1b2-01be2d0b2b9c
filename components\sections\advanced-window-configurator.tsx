"use client"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  HelpCircle,
  ChevronDown,
  ChevronUp,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize,
} from "lucide-react"
import type { Window3DViewerRef } from "@/components/ui/3d-window-viewer"

interface AdvancedWindowConfiguratorProps {
  productName: string
}

export default function AdvancedWindowConfigurator({ productName }: AdvancedWindowConfiguratorProps) {
  // State for configuration options
  const [numberOfVerticalWindows, setNumberOfVerticalWindows] = useState("1")
  const [numberOfHorizontalWindows, setNumberOfHorizontalWindows] = useState("1")
  const [selectedWindow, setSelectedWindow] = useState("W1.1")

  // Collapsible sections
  const [wholeOpeningExpanded, setWholeOpeningExpanded] = useState(true)
  const [exteriorOptionsExpanded, setExteriorOptionsExpanded] = useState(true)
  const [interiorOptionsExpanded, setInteriorOptionsExpanded] = useState(true)
  const [glassOptionsExpanded, setGlassOptionsExpanded] = useState(true)
  const [windowSpecificExpanded, setWindowSpecificExpanded] = useState(true)

  // Configuration values
  const [requireBrickmould, setRequireBrickmould] = useState("No")
  const [snapInNailingFin, setSnapInNailingFin] = useState("No")
  const [requireInteriorJamb, setRequireInteriorJamb] = useState("No")
  const [interiorReturns, setInteriorReturns] = useState("No")
  const [glazingType, setGlazingType] = useState("Double Pane Glass")
  const [lowECoating1st, setLowECoating1st] = useState("ClimaGuard 80/70 (Single)")
  const [lowECoating2nd, setLowECoating2nd] = useState("None")
  const [gasType, setGasType] = useState("Argon")
  const [requireTintFrosting, setRequireTintFrosting] = useState("None")
  const [requireSecurityGlass, setRequireSecurityGlass] = useState("None")
  const [spacerType, setSpacerType] = useState("Endur® Warm-Edge Spacer")
  const [meetsEgress, setMeetsEgress] = useState("Yes")
  const [windowType, setWindowType] = useState("Awning Window")
  const [windowWidth, setWindowWidth] = useState("25.000")
  const [hardwareType, setHardwareType] = useState("Roto Classic")
  const [hardwareColour, setHardwareColour] = useState("White")
  const [rotoCornerLock, setRotoCornerLock] = useState("NO")
  const [openingDirection, setOpeningDirection] = useState("Right")
  const [bugScreenType, setBugScreenType] = useState("Regular Screen")
  const [egressHardware, setEgressHardware] = useState("NO")
  const [specialGlazing, setSpecialGlazing] = useState("Default Glazing")
  const [requireGrills, setRequireGrills] = useState("NO")
  const [applyGrillsFor, setApplyGrillsFor] = useState("All Windows")
  const [quantity, setQuantity] = useState(1)
  const [description, setDescription] = useState("")

  // 3D Viewer ref
  const viewer3DRef = useRef<Window3DViewerRef>(null)

  // Pricing calculation
  const totalPrice = 307.26
  const windowPrice = 242.13

  const handle3DControl = (action: string) => {
    if (!viewer3DRef.current) return

    switch (action) {
      case "zoom-in":
        viewer3DRef.current.zoomIn()
        break
      case "zoom-out":
        viewer3DRef.current.zoomOut()
        break
      case "pan-left":
        viewer3DRef.current.panLeft()
        break
      case "pan-right":
        viewer3DRef.current.panRight()
        break
      case "pan-up":
        viewer3DRef.current.panUp()
        break
      case "pan-down":
        viewer3DRef.current.panDown()
        break
      case "reset":
        viewer3DRef.current.resetView()
        break
      case "rotate-left":
        viewer3DRef.current.rotateLeft()
        break
      case "rotate-right":
        viewer3DRef.current.rotateRight()
        break
      case "rotate-up":
        viewer3DRef.current.rotateUp()
        break
      case "rotate-down":
        viewer3DRef.current.rotateDown()
        break
    }
  }

  const handleAddToCart = () => {
    console.log("Adding to cart:", {
      description,
      quantity,
      totalPrice: totalPrice * quantity,
    })
    alert("Product added to cart!")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Banner */}
      <div className="bg-black text-white text-center py-3">
        <p className="font-poppins text-sm font-medium">Custom made windows in as quick as 10 business days</p>
      </div>

      {/* Main Title */}
      <div className="text-center py-8">
        <h1 className="font-bebas text-3xl md:text-5xl text-black">Build Your Own {productName}</h1>
      </div>

      <div className="max-w-[1400px] mx-auto px-4 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Window Preview & Controls */}
          <div className="space-y-4 order-1">
            {/* Window Diagram */}
            <div className="bg-gray-200 p-6 rounded-lg h-[300px] relative">
              <div className="bg-white border-2 border-gray-400 rounded-md relative h-full flex items-center justify-center">
                <div className="absolute top-2 left-2 font-poppins text-xs font-semibold text-gray-700 bg-white px-1">
                  W1.1
                </div>
                <div className="text-gray-400 text-6xl">⧄</div>
                <div className="absolute bottom-2 left-2 font-poppins text-xs text-gray-600">25"</div>
                <div className="absolute bottom-2 right-2 font-poppins text-xs text-gray-600">25"</div>
              </div>
            </div>

            {/* 3D Controls */}
            <div className="grid grid-cols-4 gap-2 bg-white p-4 rounded-lg">
              <button
                onClick={() => handle3DControl("pan-up")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Pan Up"
              >
                <ArrowUp className="w-4 h-4" />
              </button>
              <button
                onClick={() => handle3DControl("zoom-in")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Zoom In"
              >
                <ZoomIn className="w-4 h-4" />
              </button>
              <button
                onClick={() => handle3DControl("zoom-out")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Zoom Out"
              >
                <ZoomOut className="w-4 h-4" />
              </button>
              <button
                onClick={() => handle3DControl("rotate-left")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Rotate Left"
              >
                <ArrowLeft className="w-4 h-4" />
              </button>
              <button
                onClick={() => handle3DControl("rotate-right")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Rotate Right"
              >
                <ArrowRight className="w-4 h-4" />
              </button>
              <button
                onClick={() => handle3DControl("pan-down")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Pan Down"
              >
                <ArrowDown className="w-4 h-4" />
              </button>
              <button
                onClick={() => handle3DControl("reset")}
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Reset View"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
              <button
                className="flex items-center justify-center p-2 border border-gray-300 rounded hover:bg-gray-50"
                title="Maximize"
              >
                <Maximize className="w-4 h-4" />
              </button>
            </div>

            {/* Total Price */}
            <div className="bg-black text-white p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-bebas text-xl">Total Price</span>
                <span className="font-bebas text-xl">${totalPrice.toFixed(2)}</span>
              </div>
            </div>

            {/* Breakdown */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="font-poppins text-lg font-bold text-gray-800 mb-4">Breakdown</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 bg-gray-100 rounded text-sm">
                  <span className="font-poppins text-gray-800">[W#1.1] Awning Window</span>
                  <span className="font-poppins font-bold text-gray-800">${windowPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center text-primary-orange text-sm">
                  <span className="font-poppins italic">[W#1.1] Estimated Ready Date</span>
                  <span className="font-poppins italic">Tue. Aug. 19th, 2025</span>
                </div>
              </div>

              {/* Add to Cart Section */}
              <div className="flex items-center space-x-2 pt-4 mt-4 border-t border-gray-200">
                <Input
                  placeholder="Description"
                  className="flex-1 font-poppins text-sm"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
                <div className="flex items-center space-x-1">
                  <span className="font-poppins text-sm text-gray-600">QTY</span>
                  <Input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    className="w-16 text-center font-poppins text-sm"
                    min="1"
                  />
                </div>
                <Button
                  onClick={handleAddToCart}
                  className="bg-black hover:bg-gray-800 text-white font-poppins text-sm px-4 py-2"
                >
                  Add To Cart
                </Button>
              </div>
            </div>
          </div>

          {/* Center Column - Window Configuration */}
          <div className="space-y-4 order-2">
            {/* Window Configuration Header */}
            <div className="bg-black text-white text-center py-3 rounded-lg">
              <h2 className="font-bebas text-xl">Window Configuration</h2>
            </div>

            {/* Number of Vertical Windows */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <label className="font-poppins text-sm font-semibold text-gray-800">Number of Vertical Windows</label>
                <HelpCircle className="w-4 h-4 text-gray-400" />
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 border border-gray-300 rounded flex-shrink-0 bg-gray-50 flex items-center justify-center">
                  <div className="w-4 h-4 border border-gray-400"></div>
                </div>
                <Select value={numberOfVerticalWindows} onValueChange={setNumberOfVerticalWindows}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Window(s) High</SelectItem>
                    <SelectItem value="2">2 Window(s) High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Number of Horizontal Windows */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <label className="font-poppins text-sm font-semibold text-gray-800">Number of Horizontal Windows</label>
                <HelpCircle className="w-4 h-4 text-gray-400" />
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 border border-gray-300 rounded flex-shrink-0 bg-gray-50 flex items-center justify-center">
                  <div className="flex space-x-1">
                    <div className="w-2 h-4 border border-gray-400"></div>
                    <div className="w-2 h-4 border border-gray-400"></div>
                  </div>
                </div>
                <Select value={numberOfHorizontalWindows} onValueChange={setNumberOfHorizontalWindows}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Window(s) Wide</SelectItem>
                    <SelectItem value="2">2 Window(s) Wide</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Whole Opening Details */}
            <div className="bg-white rounded-lg shadow-sm">
              <div
                className="bg-black text-white p-3 rounded-t-lg cursor-pointer flex items-center justify-between"
                onClick={() => setWholeOpeningExpanded(!wholeOpeningExpanded)}
              >
                <h3 className="font-bebas text-lg">Whole Opening Details</h3>
                {wholeOpeningExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
              {wholeOpeningExpanded && (
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">
                      Overall Window Depth: 3.250"
                    </label>
                    <HelpCircle className="w-4 h-4 text-gray-400" />
                  </div>
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="font-poppins text-sm font-semibold text-gray-800">Measurement Type</label>
                        <HelpCircle className="w-4 h-4 text-gray-400" />
                      </div>
                      <Select value="Frame Size">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Frame Size">Frame Size</SelectItem>
                          <SelectItem value="Rough Opening">Rough Opening</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="font-poppins text-sm font-semibold text-gray-800 mb-2 block">
                          Frame Width
                        </label>
                        <div className="flex items-center space-x-2">
                          <Input type="number" value="25.000" className="text-center" />
                          <span className="text-sm text-gray-600">Inches</span>
                        </div>
                      </div>
                      <div>
                        <label className="font-poppins text-sm font-semibold text-gray-800 mb-2 block">
                          Frame Height
                        </label>
                        <div className="flex items-center space-x-2">
                          <Input type="number" value="25.000" className="text-center" />
                          <span className="text-sm text-gray-600">Inches</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Exterior Options */}
            <div className="bg-white rounded-lg shadow-sm">
              <div
                className="bg-black text-white p-3 rounded-t-lg cursor-pointer flex items-center justify-between"
                onClick={() => setExteriorOptionsExpanded(!exteriorOptionsExpanded)}
              >
                <h3 className="font-bebas text-lg">Exterior Options</h3>
                {exteriorOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
              {exteriorOptionsExpanded && (
                <div className="p-4 space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">
                        Do you require a Brickmould?
                      </label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={requireBrickmould} onValueChange={setRequireBrickmould}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Snap-in Nailing Fin?</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={snapInNailingFin} onValueChange={setSnapInNailingFin}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Interior Options */}
            <div className="bg-white rounded-lg shadow-sm">
              <div
                className="bg-black text-white p-3 rounded-t-lg cursor-pointer flex items-center justify-between"
                onClick={() => setInteriorOptionsExpanded(!interiorOptionsExpanded)}
              >
                <h3 className="font-bebas text-lg">Interior Options</h3>
                {interiorOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
              {interiorOptionsExpanded && (
                <div className="p-4 space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">
                        Do you require Interior Jamb?
                      </label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={requireInteriorJamb} onValueChange={setRequireInteriorJamb}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Interior Returns</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={interiorReturns} onValueChange={setInteriorReturns}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="No">No</SelectItem>
                          <SelectItem value="Yes">Yes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Glass Options */}
            <div className="bg-white rounded-lg shadow-sm">
              <div
                className="bg-black text-white p-3 rounded-t-lg cursor-pointer flex items-center justify-between"
                onClick={() => setGlassOptionsExpanded(!glassOptionsExpanded)}
              >
                <h3 className="font-bebas text-lg">Glass Options</h3>
                {glassOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
              {glassOptionsExpanded && (
                <div className="p-4 space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Choose Glazing Type</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-50 border border-blue-300 rounded flex items-center justify-center">
                        <svg viewBox="0 0 20 20" className="w-4 h-4 text-blue-500">
                          <rect x="2" y="2" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" />
                        </svg>
                      </div>
                      <Select value={glazingType} onValueChange={setGlazingType}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Double Pane Glass">Double Pane Glass</SelectItem>
                          <SelectItem value="Triple Pane Glass">Triple Pane Glass</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">1st Pane Low-E Coating</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-8 bg-blue-100 border border-blue-300 rounded flex items-center justify-center">
                        <span className="text-xs text-blue-600 font-bold">CG</span>
                      </div>
                      <Select value={lowECoating1st} onValueChange={setLowECoating1st}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ClimaGuard 80/70 (Single)">ClimaGuard 80/70 (Single)</SelectItem>
                          <SelectItem value="None">None</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">2nd Pane Low-E Coating</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={lowECoating2nd} onValueChange={setLowECoating2nd}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="None">None</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Select Gas Type</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-50 border border-blue-300 rounded flex items-center justify-center">
                        <svg viewBox="0 0 20 20" className="w-4 h-4 text-blue-500">
                          <circle cx="10" cy="10" r="6" fill="none" stroke="currentColor" strokeWidth="2" />
                        </svg>
                      </div>
                      <Select value={gasType} onValueChange={setGasType}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Argon">Argon</SelectItem>
                          <SelectItem value="Air">Air</SelectItem>
                          <SelectItem value="Krypton">Krypton</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">
                        Do you require Tint or Frosting?
                      </label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={requireTintFrosting} onValueChange={setRequireTintFrosting}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="None">None</SelectItem>
                          <SelectItem value="Tint">Tint</SelectItem>
                          <SelectItem value="Frosting">Frosting</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">
                        Do you require Security Glass?
                      </label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={requireSecurityGlass} onValueChange={setRequireSecurityGlass}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="None">None</SelectItem>
                          <SelectItem value="Tempered">Tempered</SelectItem>
                          <SelectItem value="Laminated">Laminated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Spacer Type</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded flex items-center justify-center">
                        <div className="w-4 h-2 bg-gray-400 rounded-sm"></div>
                      </div>
                      <Select value={spacerType} onValueChange={setSpacerType}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Endur® Warm-Edge Spacer">Endur® Warm-Edge Spacer</SelectItem>
                          <SelectItem value="Standard Spacer">Standard Spacer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Window Specific Options */}
          <div className="space-y-4 order-3">
            {/* Window #1.1 Specific Options */}
            <div className="bg-white rounded-lg shadow-sm">
              <div
                className="bg-black text-white p-3 rounded-t-lg cursor-pointer flex items-center justify-between"
                onClick={() => setWindowSpecificExpanded(!windowSpecificExpanded)}
              >
                <h3 className="font-bebas text-lg">Window #1.1 Specific Options</h3>
                {windowSpecificExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
              {windowSpecificExpanded && (
                <div className="p-4 space-y-4">
                  {/* Energy Ratings */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-poppins text-sm font-semibold text-gray-800">Energy Ratings</h4>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="grid grid-cols-3 gap-2 mb-4">
                      <div className="text-center p-3 border border-gray-200 rounded">
                        <div className="font-poppins text-xs font-semibold text-gray-600">ER</div>
                        <div className="font-poppins text-2xl font-bold text-gray-800">34</div>
                      </div>
                      <div className="text-center p-3 border border-gray-200 rounded">
                        <div className="font-poppins text-xs font-semibold text-gray-600">SHGC</div>
                        <div className="font-poppins text-2xl font-bold text-gray-800">0.47</div>
                      </div>
                      <div className="text-center p-3 border border-gray-200 rounded">
                        <div className="font-poppins text-xs font-semibold text-gray-600">VT</div>
                        <div className="font-poppins text-2xl font-bold text-gray-800">0.52</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-center p-3 border border-gray-200 rounded">
                        <div className="font-poppins text-xs font-semibold text-gray-600">U-Factor (I-P)</div>
                        <div className="font-poppins text-2xl font-bold text-gray-800">1.51</div>
                      </div>
                      <div className="text-center p-3 border border-gray-200 rounded">
                        <div className="font-poppins text-xs font-semibold text-gray-600">U-Factor (SI)</div>
                        <div className="font-poppins text-2xl font-bold text-gray-800">0.27</div>
                      </div>
                    </div>
                  </div>

                  {/* NRCan Model # */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="mb-2">
                      <span className="font-poppins text-xs font-semibold text-gray-600">NRCan Model #</span>
                    </div>
                    <div className="font-poppins text-sm text-gray-800 mb-4">PWM-AW-3,CL-3,8071(3)-16AR97SP</div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="font-poppins text-xs font-semibold text-gray-600">Most Efficient 2025</div>
                        <div className="font-poppins text-sm text-gray-800">N</div>
                      </div>
                      <div>
                        <div className="font-poppins text-xs font-semibold text-gray-600">NRCan Reference #</div>
                        <div className="font-poppins text-sm text-gray-800">NR10905-35751797-ESS</div>
                      </div>
                    </div>
                  </div>

                  {/* Window Type */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Window Type</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-orange border border-primary-orange rounded flex items-center justify-center">
                        <div className="w-4 h-4 bg-white rounded-sm"></div>
                      </div>
                      <Select value={windowType} onValueChange={setWindowType}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Awning Window">Awning Window</SelectItem>
                          <SelectItem value="Casement Window">Casement Window</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Window Width */}
                  <div>
                    <h4 className="font-poppins text-sm font-semibold text-gray-800 mb-3">Window Width</h4>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <div className="font-poppins text-xs text-gray-600">Min. Width</div>
                        <div className="font-poppins text-sm font-semibold">14.250"</div>
                      </div>
                      <div>
                        <div className="font-poppins text-xs text-gray-600">Max Width</div>
                        <div className="font-poppins text-sm font-semibold">36.000"</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <div className="font-poppins text-xs text-gray-600">Min. Height</div>
                        <div className="font-poppins text-sm font-semibold">13.000"</div>
                      </div>
                      <div>
                        <div className="font-poppins text-xs text-gray-600">Max Height</div>
                        <div className="font-poppins text-sm font-semibold">90.000"</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        value={windowWidth}
                        onChange={(e) => setWindowWidth(e.target.value)}
                        className="flex-1 text-center font-poppins text-lg font-bold"
                      />
                      <span className="font-poppins text-sm text-gray-600">Inches</span>
                    </div>
                  </div>

                  {/* Hardware Type */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Hardware Type</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-8 bg-red-100 border border-red-300 rounded flex items-center justify-center">
                        <span className="text-xs text-red-600 font-bold">Roto</span>
                      </div>
                      <Select value={hardwareType} onValueChange={setHardwareType}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Roto Classic">Roto Classic</SelectItem>
                          <SelectItem value="Roto Premium">Roto Premium</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Hardware Colour */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Hardware Colour</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-white border border-gray-300 rounded"></div>
                      <Select value={hardwareColour} onValueChange={setHardwareColour}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="White">White</SelectItem>
                          <SelectItem value="Black">Black</SelectItem>
                          <SelectItem value="Bronze">Bronze</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Roto Corner Lock */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Roto Corner Lock</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                        <div className="text-xs text-gray-600">😊</div>
                      </div>
                      <Select value={rotoCornerLock} onValueChange={setRotoCornerLock}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="NO">NO</SelectItem>
                          <SelectItem value="YES">YES</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Type Of Bug Screen */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Type of Bug Screen</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                      <Select value={bugScreenType} onValueChange={setBugScreenType}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Regular Screen">Regular Screen</SelectItem>
                          <SelectItem value="Pet Screen">Pet Screen</SelectItem>
                          <SelectItem value="Solar Screen">Solar Screen</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Special Glazing Options */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">
                        Special Glazing Options
                      </label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                      <Select value={specialGlazing} onValueChange={setSpecialGlazing}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Default Glazing">Default Glazing</SelectItem>
                          <SelectItem value="Custom Glazing">Custom Glazing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Do You Require Grills? */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-poppins text-sm font-semibold text-gray-800">Do you require grills?</label>
                      <HelpCircle className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                      <Select value={requireGrills} onValueChange={setRequireGrills}>
                        <SelectTrigger className="flex-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="NO">NO</SelectItem>
                          <SelectItem value="YES">YES</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
