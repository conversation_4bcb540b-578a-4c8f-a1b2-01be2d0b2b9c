"use client"

import { useEffect } from "react"

export default function TawkChat() {
  useEffect(() => {
    // Tawk.to integration script
    const script = document.createElement("script")
    script.async = true
    script.src = "https://embed.tawk.to/YOUR_TAWK_ID/YOUR_WIDGET_ID"
    script.charset = "UTF-8"
    script.setAttribute("crossorigin", "*")

    // Add the script to the document
    document.head.appendChild(script)

    // Initialize Tawk_API
    ;(window as any).Tawk_API = (window as any).Tawk_API || {}
    ;(window as any).Tawk_LoadStart = new Date()

    // Optional: Customize the widget
    ;(window as any).Tawk_API.onLoad = () => {
      console.log("Tawk.to chat widget loaded")
    }

    // Optional: Handle chat events
    ;(window as any).Tawk_API.onChatStarted = () => {
      console.log("Chat started")
    }
    ;(window as any).Tawk_API.onChatEnded = () => {
      console.log("Chat ended")
    }

    // Cleanup function
    return () => {
      // Remove the script when component unmounts
      const existingScript = document.querySelector(`script[src*="embed.tawk.to"]`)
      if (existingScript) {
        existingScript.remove()
      }
    }
  }, [])

  return null // This component doesn't render anything visible
}
