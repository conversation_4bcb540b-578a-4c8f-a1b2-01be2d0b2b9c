"use client"

import { useState, useEffect, useRef } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  ChevronDown,
  ChevronUp,
  ZoomIn,
  ZoomOut,
  Ruler,
  RotateCcw,
  Maximize,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
} from "lucide-react"
import Window3DViewer, { type Window3DViewerRef } from "@/components/ui/3d-window-viewer"

interface WindowDetailsConfiguratorProps {
  productName: string
  frameWidth: number
  frameHeight: number
  numberOfVerticalWindows: number
  numberOfHorizontalWindows: number
}

// Enhanced Smart Pricing Configuration
const PRICING_CONFIG = {
  // Base pricing per square foot with material quality tiers
  basePricePerSqFt: {
    standard: 12.5,
    premium: 15.8,
    luxury: 19.2,
  },

  // Enhanced color multipliers for exterior finish
  colorMultipliers: {
    white: 1.0,
    black: 1.18, // Premium black finish
    beige: 1.1,
    brown: 1.14,
    gray: 1.12,
    bronze: 1.22, // Premium bronze finish
    green: 1.16,
    custom: 1.35, // Custom color option
  },

  // Interior finish additional cost (if different from exterior)
  interiorFinishCost: {
    white: 0,
    black: 18.5,
    beige: 14.25,
    brown: 16.75,
    gray: 15.0,
    bronze: 22.5,
    green: 19.25,
    custom: 28.0,
  },

  // Enhanced glass options pricing with energy efficiency tiers
  glazingOptions: {
    "double-pane": {
      standard: 22.97,
      lowE: 28.45,
      premium: 34.8,
    },
    "triple-pane": {
      standard: 48.0,
      lowE: 56.25,
      premium: 68.5,
    },
  },

  // Low-E coating pricing with performance tiers
  lowECoating: {
    none: 0,
    "climaguard-80-70": 28.5,
    "climaguard-premium": 35.75,
    "solar-control": 42.0,
  },

  // Gas fill pricing with performance benefits
  gasFill: {
    air: 0,
    argon: 18.75,
    krypton: 48.0,
    xenon: 72.5, // Premium gas option
  },

  // Window type pricing with complexity factors
  windowType: {
    "awning-window": 0,
    "casement-window": 22.75,
    "double-hung": 28.5,
    sliding: 18.25,
  },

  // Hardware type pricing with quality tiers
  hardwareType: {
    "roto-classic": 0,
    "roto-premium": 32.5,
    "roto-luxury": 58.0,
    "custom-hardware": 85.0,
  },

  // Hardware color pricing
  hardwareColor: {
    white: 0,
    black: 10.5,
    beige: 8.25,
    brown: 9.75,
    gray: 8.5,
    bronze: 15.5,
    green: 12.25,
    custom: 22.0,
  },

  // Enhanced additional options
  brickmould: {
    standard: 35.5,
    premium: 48.75,
    custom: 68.0,
  },
  snapInNailingFin: 26.75,
  tintFrosting: {
    none: 0,
    tint: 32.0,
    frosting: 42.5,
    decorative: 58.0,
  },
  securityGlass: {
    none: 0,
    tempered: 52.75,
    laminated: 78.5,
    bulletResistant: 145.0,
  },
  spacerType: {
    standard: 0,
    "warm-edge": 22.25,
    "super-spacer": 35.0,
    aluminum: -6.5,
  },
  rotoCornerLock: 28.75,
  bugScreen: {
    "regular-screen": 22.5,
    "pet-screen": 34.75,
    "solar-screen": 48.25,
    retractable: 85.0,
  },
  specialGlazing: {
    "default-glazing": 0,
    "custom-glazing": 65.0,
    "smart-glass": 185.0,
  },
  grills: {
    basic: 42.5,
    decorative: 68.0,
    custom: 95.0,
  },

  // Enhanced size-based multipliers
  sizeMultipliers: {
    small: 0.92, // < 8 sq ft
    medium: 1.0, // 8-20 sq ft
    large: 1.1, // 20-35 sq ft
    xlarge: 1.18, // 35-50 sq ft
    xxlarge: 1.28, // > 50 sq ft
  },

  // Enhanced complexity multipliers
  complexityMultipliers: {
    basic: 1.0, // 0-2 upgrades
    moderate: 1.08, // 3-5 upgrades
    complex: 1.15, // 6-8 upgrades
    premium: 1.25, // 9+ upgrades
  },

  // Quality tier multipliers
  qualityTiers: {
    standard: 1.0,
    premium: 1.15,
    luxury: 1.35,
  },

  // Installation complexity factors
  installationFactors: {
    standard: 1.0,
    complex: 1.12,
    custom: 1.25,
  },

  // Seasonal pricing adjustments
  seasonalAdjustments: {
    peak: 1.08, // Spring/Summer
    standard: 1.0, // Fall
    discount: 0.95, // Winter
  },
}

export default function WindowDetailsConfigurator({
  productName,
  frameWidth,
  frameHeight,
  numberOfVerticalWindows,
  numberOfHorizontalWindows,
}: WindowDetailsConfiguratorProps) {
  // State for all configuration options
  const [glazingType, setGlazingType] = useState("Double Pane Glass")
  const [lowECoating1st, setLowECoating1st] = useState("ClimaGuard 80/70 (Single)")
  const [lowECoating2nd, setLowECoating2nd] = useState("None")
  const [gasType, setGasType] = useState("Argon")
  const [requireBrickmould, setRequireBrickmould] = useState("No")
  const [snapInNailingFin, setSnapInNailingFin] = useState("No")
  const [windowType, setWindowType] = useState("Awning Window")
  const [hardwareType, setHardwareType] = useState("Roto Classic")
  const [hardwareColour, setHardwareColour] = useState("White")
  const [requireTintFrosting, setRequireTintFrosting] = useState("None")
  const [requireSecurityGlass, setRequireSecurityGlass] = useState("None")
  const [spacerType, setSpacerType] = useState("Endur® Warm-Edge Spacer")
  const [rotoCornerLock, setRotoCornerLock] = useState("NO")
  const [bugScreenType, setBugScreenType] = useState("Regular Screen")
  const [specialGlazing, setSpecialGlazing] = useState("Default Glazing")
  const [requireGrills, setRequireGrills] = useState("NO")
  const [interiorFinish, setInteriorFinish] = useState("white")
  const [exteriorFinish, setExteriorFinish] = useState("white")
  const [quantity, setQuantity] = useState(1)
  const [currentFrameWidth, setCurrentFrameWidth] = useState(frameWidth)
  const [currentFrameHeight, setCurrentFrameHeight] = useState(frameHeight)
  const [description, setDescription] = useState("")
  const [isFullScreen, setIsFullScreen] = useState(false)
  const [qualityTier, setQualityTier] = useState("standard")

  // State for UI sections
  const [wholeOpeningExpanded, setWholeOpeningExpanded] = useState(false)
  const [exteriorOptionsExpanded, setExteriorOptionsExpanded] = useState(true)
  const [interiorOptionsExpanded, setInteriorOptionsExpanded] = useState(false)
  const [glassOptionsExpanded, setGlassOptionsExpanded] = useState(true)
  const [specificOptionsExpanded, setSpecificOptionsExpanded] = useState(true)
  const [showMeasurements, setShowMeasurements] = useState(true)

  // 3D Viewer ref
  const viewer3DRef = useRef<Window3DViewerRef>(null)

  // Enhanced Smart Pricing System
  const calculateSmartPrice = () => {
    // Calculate window area in square feet
    const areaInches = currentFrameWidth * currentFrameHeight
    const areaSqFt = areaInches / 144 // Convert to square feet

    // Determine quality tier
    const qualityMultiplier = PRICING_CONFIG.qualityTiers[qualityTier as keyof typeof PRICING_CONFIG.qualityTiers]

    // Base price calculation with quality tier
    let basePrice =
      areaSqFt *
      PRICING_CONFIG.basePricePerSqFt[qualityTier as keyof typeof PRICING_CONFIG.basePricePerSqFt] *
      qualityMultiplier

    // Apply color multiplier (exterior finish affects base price)
    const colorMultiplier =
      PRICING_CONFIG.colorMultipliers[exteriorFinish as keyof typeof PRICING_CONFIG.colorMultipliers] || 1.0
    basePrice *= colorMultiplier

    // Interior finish additional cost (if different from exterior)
    let interiorColorCost = 0
    if (interiorFinish !== exteriorFinish) {
      interiorColorCost =
        PRICING_CONFIG.interiorFinishCost[interiorFinish as keyof typeof PRICING_CONFIG.interiorFinishCost] || 0
    }

    // Enhanced glass options with quality tiers
    const glazingKey = glazingType === "Triple Pane Glass" ? "triple-pane" : "double-pane"
    const glazingQuality = lowECoating1st !== "None" ? "premium" : "standard"
    const glazingCost =
      PRICING_CONFIG.glazingOptions[glazingKey][
        glazingQuality as keyof (typeof PRICING_CONFIG.glazingOptions)[typeof glazingKey]
      ]

    // Low-E coating with enhanced options
    const lowECost = lowECoating1st !== "None" ? PRICING_CONFIG.lowECoating["climaguard-80-70"] : 0

    // Gas fill with premium options
    const gasCost =
      gasType === "Argon"
        ? PRICING_CONFIG.gasFill.argon
        : gasType === "Krypton"
          ? PRICING_CONFIG.gasFill.krypton
          : gasType === "Xenon"
            ? PRICING_CONFIG.gasFill.xenon
            : 0

    // Window type
    const windowTypeCost = windowType === "Casement Window" ? PRICING_CONFIG.windowType["casement-window"] : 0

    // Hardware type with enhanced options
    const hardwareTypeCost =
      hardwareType === "Roto Premium"
        ? PRICING_CONFIG.hardwareType["roto-premium"]
        : hardwareType === "Roto Luxury"
          ? PRICING_CONFIG.hardwareType["roto-luxury"]
          : 0

    // Hardware color
    const hardwareColorCost =
      PRICING_CONFIG.hardwareColor[hardwareColour.toLowerCase() as keyof typeof PRICING_CONFIG.hardwareColor] || 0

    // Enhanced additional options
    const brickmouldCost = requireBrickmould === "Yes" ? PRICING_CONFIG.brickmould.standard : 0
    const nailingFinCost = snapInNailingFin === "Yes" ? PRICING_CONFIG.snapInNailingFin : 0
    const tintFrostingCost =
      requireTintFrosting === "Tint"
        ? PRICING_CONFIG.tintFrosting.tint
        : requireTintFrosting === "Frosting"
          ? PRICING_CONFIG.tintFrosting.frosting
          : requireTintFrosting === "Decorative"
            ? PRICING_CONFIG.tintFrosting.decorative
            : 0
    const securityGlassCost =
      requireSecurityGlass === "Tempered"
        ? PRICING_CONFIG.securityGlass.tempered
        : requireSecurityGlass === "Laminated"
          ? PRICING_CONFIG.securityGlass.laminated
          : 0
    const spacerCost = spacerType === "Endur® Warm-Edge Spacer" ? PRICING_CONFIG.spacerType["warm-edge"] : 0
    const cornerLockCost = rotoCornerLock === "YES" ? PRICING_CONFIG.rotoCornerLock : 0
    const bugScreenCost =
      bugScreenType === "Pet Screen"
        ? PRICING_CONFIG.bugScreen["pet-screen"]
        : bugScreenType === "Solar Screen"
          ? PRICING_CONFIG.bugScreen["solar-screen"]
          : bugScreenType === "Retractable"
            ? PRICING_CONFIG.bugScreen["retractable"]
            : PRICING_CONFIG.bugScreen["regular-screen"]
    const specialGlazingCost =
      specialGlazing === "Custom Glazing"
        ? PRICING_CONFIG.specialGlazing["custom-glazing"]
        : specialGlazing === "Smart Glass"
          ? PRICING_CONFIG.specialGlazing["smart-glass"]
          : 0
    const grillsCost = requireGrills === "YES" ? PRICING_CONFIG.grills.basic : 0

    // Enhanced size-based multiplier
    let sizeMultiplier = PRICING_CONFIG.sizeMultipliers.medium
    if (areaSqFt < 8) sizeMultiplier = PRICING_CONFIG.sizeMultipliers.small
    else if (areaSqFt > 50) sizeMultiplier = PRICING_CONFIG.sizeMultipliers.xxlarge
    else if (areaSqFt > 35) sizeMultiplier = PRICING_CONFIG.sizeMultipliers.xlarge
    else if (areaSqFt > 20) sizeMultiplier = PRICING_CONFIG.sizeMultipliers.large

    // Enhanced complexity multiplier
    const upgradesSelected = [
      requireBrickmould === "Yes",
      snapInNailingFin === "Yes",
      requireTintFrosting !== "None",
      requireSecurityGlass !== "None",
      rotoCornerLock === "YES",
      requireGrills === "YES",
      specialGlazing !== "Default Glazing",
      hardwareType !== "Roto Classic",
      glazingType !== "Double Pane Glass",
      gasType !== "Air",
      lowECoating1st !== "None",
      exteriorFinish !== "white",
      interiorFinish !== "white",
      spacerType !== "Standard Spacer",
      windowType !== "Awning Window",
      hardwareColour !== "White",
      bugScreenType !== "Regular Screen",
      qualityTier !== "standard",
    ].filter(Boolean).length

    let complexityMultiplier = PRICING_CONFIG.complexityMultipliers.basic
    if (upgradesSelected >= 9) complexityMultiplier = PRICING_CONFIG.complexityMultipliers.premium
    else if (upgradesSelected >= 6) complexityMultiplier = PRICING_CONFIG.complexityMultipliers.complex
    else if (upgradesSelected >= 3) complexityMultiplier = PRICING_CONFIG.complexityMultipliers.moderate

    // Installation complexity
    const installationMultiplier =
      upgradesSelected >= 8
        ? PRICING_CONFIG.installationFactors.custom
        : upgradesSelected >= 4
          ? PRICING_CONFIG.installationFactors.complex
          : PRICING_CONFIG.installationFactors.standard

    // Seasonal adjustment (simulate current season as standard)
    const seasonalMultiplier = PRICING_CONFIG.seasonalAdjustments.standard

    // Calculate subtotals
    const subtotals = {
      basePrice: basePrice,
      interiorColorCost,
      glazingCost,
      lowECost,
      gasCost,
      windowTypeCost,
      hardwareTypeCost,
      hardwareColorCost,
      brickmouldCost,
      nailingFinCost,
      tintFrostingCost,
      securityGlassCost,
      spacerCost,
      cornerLockCost,
      bugScreenCost,
      specialGlazingCost,
      grillsCost,
    }

    // Calculate total before multipliers
    const subtotal = Object.values(subtotals).reduce((sum, cost) => sum + cost, 0)

    // Apply all multipliers
    const totalWithMultipliers =
      subtotal * sizeMultiplier * complexityMultiplier * installationMultiplier * seasonalMultiplier

    return {
      subtotals,
      sizeMultiplier,
      complexityMultiplier,
      installationMultiplier,
      seasonalMultiplier,
      qualityMultiplier,
      areaSqFt,
      totalPrice: totalWithMultipliers,
      pricePerSqFt: totalWithMultipliers / areaSqFt,
      upgradesCount: upgradesSelected,
      savings: subtotal * 0.15, // Potential savings shown
    }
  }

  // Enhanced energy ratings calculation
  const calculateEnergyRatings = () => {
    let baseER = 25
    let baseSHGC = 0.55
    let baseVT = 0.65
    let baseUFactorIP = 1.8

    // Quality tier adjustments
    if (qualityTier === "premium") {
      baseER += 5
      baseSHGC -= 0.03
      baseVT += 0.02
      baseUFactorIP -= 0.1
    } else if (qualityTier === "luxury") {
      baseER += 12
      baseSHGC -= 0.06
      baseVT += 0.05
      baseUFactorIP -= 0.2
    }

    // Glazing type adjustments
    if (glazingType === "Triple Pane Glass") {
      baseER += 18
      baseSHGC -= 0.1
      baseVT -= 0.12
      baseUFactorIP -= 0.5
    }

    // Low-E coating adjustments
    if (lowECoating1st !== "None") {
      baseER += 10
      baseSHGC -= 0.07
      baseVT -= 0.1
      baseUFactorIP -= 0.25
    }

    // Gas fill adjustments
    if (gasType === "Argon") {
      baseER += 4
      baseUFactorIP -= 0.12
    } else if (gasType === "Krypton") {
      baseER += 8
      baseUFactorIP -= 0.18
    } else if (gasType === "Xenon") {
      baseER += 12
      baseUFactorIP -= 0.25
    }

    // Spacer type adjustments
    if (spacerType === "Endur® Warm-Edge Spacer") {
      baseER += 3
      baseUFactorIP -= 0.05
    }

    return {
      er: Math.max(15, Math.round(baseER)),
      shgc: Math.max(0.25, Math.round(baseSHGC * 100) / 100),
      vt: Math.max(0.3, Math.round(baseVT * 100) / 100),
      uFactorIP: Math.max(0.15, Math.round(baseUFactorIP * 100) / 100),
      uFactorSI: Math.max(0.85, Math.round(baseUFactorIP * 5.678 * 100) / 100),
    }
  }

  const [pricing, setPricing] = useState(calculateSmartPrice())
  const [energyRatings, setEnergyRatings] = useState(calculateEnergyRatings())

  // Update pricing and energy ratings when any configuration changes
  useEffect(() => {
    const newPricing = calculateSmartPrice()
    const newEnergyRatings = calculateEnergyRatings()

    setPricing(newPricing)
    setEnergyRatings(newEnergyRatings)
  }, [
    currentFrameWidth,
    currentFrameHeight,
    exteriorFinish,
    interiorFinish,
    glazingType,
    lowECoating1st,
    lowECoating2nd,
    gasType,
    requireBrickmould,
    snapInNailingFin,
    windowType,
    hardwareType,
    hardwareColour,
    requireTintFrosting,
    requireSecurityGlass,
    spacerType,
    rotoCornerLock,
    bugScreenType,
    specialGlazing,
    requireGrills,
    qualityTier,
  ])

  // 3D Viewer control functions - Updated to use rotation instead of panning
  const handle3DControl = (action: string) => {
    if (!viewer3DRef.current) return

    switch (action) {
      case "zoom-in":
        viewer3DRef.current.zoomIn()
        break
      case "zoom-out":
        viewer3DRef.current.zoomOut()
        break
      case "rotate-up":
        viewer3DRef.current.rotateUp()
        break
      case "rotate-down":
        viewer3DRef.current.rotateDown()
        break
      case "rotate-left":
        viewer3DRef.current.rotateLeft()
        break
      case "rotate-right":
        viewer3DRef.current.rotateRight()
        break
      case "reset":
        viewer3DRef.current.resetView()
        break
      case "toggle-measurements":
        setShowMeasurements((prev) => !prev)
        viewer3DRef.current.toggleMeasurements()
        break
      case "toggle-fullscreen":
        setIsFullScreen((prev) => !prev)
        break
    }
  }

  const handleAddToCart = () => {
    const currentConfig = {
      productName,
      frameWidth: currentFrameWidth,
      frameHeight: currentFrameHeight,
      numberOfVerticalWindows,
      numberOfHorizontalWindows,
      glazingType,
      lowECoating1st,
      lowECoating2nd,
      gasType,
      requireBrickmould,
      snapInNailingFin,
      windowType,
      hardwareType,
      hardwareColour,
      requireTintFrosting,
      requireSecurityGlass,
      spacerType,
      rotoCornerLock,
      bugScreenType,
      specialGlazing,
      requireGrills,
      interiorFinish,
      exteriorFinish,
      quantity,
      pricing,
      energyRatings,
      description,
      qualityTier,
    }
    console.log("Adding to cart:", currentConfig)
    alert("Product added to cart! Check console for details.")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-black text-white text-center py-3">
        <p className="font-poppins text-sm font-medium">Custom Made Windows In As Quick As 10 Business Days</p>
      </div>

      {/* Main Title */}
      <div className="text-center py-8">
        <h1 className="font-bebas text-3xl md:text-5xl text-primary-orange">Build Your Own {productName}</h1>
      </div>

      {/* Full Screen Modal */}
      {isFullScreen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex flex-col">
          {/* Full Screen Header */}
          <div className="bg-primary-orange p-4 flex justify-between items-center">
            <h2 className="font-bebas text-xl text-white">3D Window Viewer - Full Screen</h2>
            <button onClick={() => setIsFullScreen(false)} className="text-white hover:bg-orange-600 p-2 rounded">
              ✕
            </button>
          </div>

          {/* Full Screen 3D Viewer */}
          <div className="flex-1 relative">
            <Window3DViewer
              ref={viewer3DRef}
              glazingType={glazingType}
              gasType={gasType}
              hardwareColour={hardwareColour}
              requireGrills={requireGrills}
              frameWidth={currentFrameWidth}
              frameHeight={currentFrameHeight}
              interiorFinish={interiorFinish}
              exteriorFinish={exteriorFinish}
              showMeasurements={showMeasurements}
              isFullScreen={true}
            />
          </div>

          {/* Full Screen Controls - Updated for rotation */}
          <div className="bg-primary-orange p-4">
            <div className="flex items-center justify-center space-x-6">
              {/* Zoom Controls */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handle3DControl("zoom-in")}
                  className="flex items-center justify-center p-3 text-white hover:bg-orange-600 rounded-full transition-colors"
                  title="Zoom In"
                >
                  <ZoomIn className="w-6 h-6" />
                </button>
                <button
                  onClick={() => handle3DControl("zoom-out")}
                  className="flex items-center justify-center p-3 text-white hover:bg-orange-600 rounded-full transition-colors"
                  title="Zoom Out"
                >
                  <ZoomOut className="w-6 h-6" />
                </button>
              </div>

              {/* Rotation Controls */}
              <div className="grid grid-cols-3 gap-0.5">
                <div></div>
                <button
                  onClick={() => handle3DControl("rotate-up")}
                  className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                  title="Rotate Up"
                >
                  <ArrowUp className="w-5 h-5" />
                </button>
                <div></div>
                <button
                  onClick={() => handle3DControl("rotate-left")}
                  className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                  title="Rotate Left"
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
                <div></div>
                <button
                  onClick={() => handle3DControl("rotate-right")}
                  className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                  title="Rotate Right"
                >
                  <ArrowRight className="w-5 h-5" />
                </button>
                <div></div>
                <button
                  onClick={() => handle3DControl("rotate-down")}
                  className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                  title="Rotate Down"
                >
                  <ArrowDown className="w-5 h-5" />
                </button>
                <div></div>
              </div>

              {/* Toggle Measurements */}
              <button
                onClick={() => handle3DControl("toggle-measurements")}
                className={`flex items-center justify-center p-3 rounded-full transition-colors ${
                  showMeasurements ? "bg-orange-600 text-white" : "text-white hover:bg-orange-600"
                }`}
                title="Toggle Measurements"
              >
                <Ruler className="w-6 h-6" />
              </button>

              {/* Reset View */}
              <button
                onClick={() => handle3DControl("reset")}
                className="flex items-center justify-center p-3 text-white hover:bg-orange-600 rounded-full transition-colors"
                title="Reset View"
              >
                <RotateCcw className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-[1400px] mx-auto px-4 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - 3D Viewer & Controls */}
          <div className="space-y-4 order-1">
            {/* Window 3D Viewer */}
            <div className="bg-gray-200 p-6 rounded-lg h-[400px] relative">
              <Window3DViewer
                ref={viewer3DRef}
                glazingType={glazingType}
                gasType={gasType}
                hardwareColour={hardwareColour}
                requireGrills={requireGrills}
                frameWidth={currentFrameWidth}
                frameHeight={currentFrameHeight}
                interiorFinish={interiorFinish}
                exteriorFinish={exteriorFinish}
                showMeasurements={showMeasurements}
                isFullScreen={false}
              />

              {/* Full Screen Button */}
              <button
                onClick={() => handle3DControl("toggle-fullscreen")}
                className="absolute top-2 right-2 bg-primary-orange text-white p-2 rounded hover:bg-orange-600 transition-colors"
                title="Full Screen"
              >
                <Maximize className="w-4 h-4" />
              </button>
            </div>

            {/* 3D Viewer Controls - Updated for rotation */}
            <div className="bg-primary-orange p-4 rounded-lg">
              <div className="flex items-center justify-center space-x-6">
                {/* Zoom Controls */}
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handle3DControl("zoom-in")}
                    className="flex items-center justify-center p-3 text-white hover:bg-orange-600 rounded-full transition-colors"
                    title="Zoom In"
                  >
                    <ZoomIn className="w-6 h-6" />
                  </button>
                  <button
                    onClick={() => handle3DControl("zoom-out")}
                    className="flex items-center justify-center p-3 text-white hover:bg-orange-600 rounded-full transition-colors"
                    title="Zoom Out"
                  >
                    <ZoomOut className="w-6 h-6" />
                  </button>
                </div>

                {/* Rotation Controls with very small gaps */}
                <div className="grid grid-cols-3 gap-0.5">
                  <div></div>
                  <button
                    onClick={() => handle3DControl("rotate-up")}
                    className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                    title="Rotate Up"
                  >
                    <ArrowUp className="w-5 h-5" />
                  </button>
                  <div></div>
                  <button
                    onClick={() => handle3DControl("rotate-left")}
                    className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                    title="Rotate Left"
                  >
                    <ArrowLeft className="w-5 h-5" />
                  </button>
                  <div></div>
                  <button
                    onClick={() => handle3DControl("rotate-right")}
                    className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                    title="Rotate Right"
                  >
                    <ArrowRight className="w-5 h-5" />
                  </button>
                  <div></div>
                  <button
                    onClick={() => handle3DControl("rotate-down")}
                    className="flex items-center justify-center p-2 text-white hover:bg-orange-600 rounded transition-colors"
                    title="Rotate Down"
                  >
                    <ArrowDown className="w-5 h-5" />
                  </button>
                  <div></div>
                </div>

                {/* Toggle Measurements */}
                <button
                  onClick={() => handle3DControl("toggle-measurements")}
                  className={`flex items-center justify-center p-3 rounded-full transition-colors ${
                    showMeasurements ? "bg-orange-600 text-white" : "text-white hover:bg-orange-600"
                  }`}
                  title="Toggle Measurements"
                >
                  <Ruler className="w-6 h-6" />
                </button>

                {/* Reset View */}
                <button
                  onClick={() => handle3DControl("reset")}
                  className="flex items-center justify-center p-3 text-white hover:bg-orange-600 rounded-full transition-colors"
                  title="Reset View"
                >
                  <RotateCcw className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Enhanced Total Price */}
            <div className="bg-primary-orange text-white p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="font-bebas text-2xl">Total Price</span>
                <span className="font-bebas text-2xl">${pricing.totalPrice.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-sm opacity-90">
                <span>Per Sq Ft: ${pricing.pricePerSqFt.toFixed(2)}</span>
                <span>Area: {pricing.areaSqFt.toFixed(1)} sq ft</span>
              </div>
              {pricing.savings > 0 && (
                <div className="text-center mt-2 text-sm bg-orange-600 rounded px-2 py-1">
                  Potential Savings: ${pricing.savings.toFixed(2)}
                </div>
              )}
            </div>

            {/* Enhanced Breakdown */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="font-poppins text-lg font-bold text-gray-800 mb-4">Enhanced Breakdown</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.1] {windowType}</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.basePrice / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-primary-orange">
                  <span className="font-poppins text-sm italic">[W#1.1] Estimated Ready Date</span>
                  <span className="font-poppins text-sm italic">Thu. May. 29th, 2025</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.1] 1st Pane Low-E Coating</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.lowECost / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.1] Gas Options</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.gasCost / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.1] Screen Option</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.bugScreenCost / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-primary-orange text-white rounded">
                  <span className="font-poppins text-sm">[W#1.2] {windowType}</span>
                  <span className="font-poppins text-sm font-bold">
                    ${(pricing.subtotals.basePrice / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center text-primary-orange">
                  <span className="font-poppins text-sm italic">[W#1.2] Estimated Ready Date</span>
                  <span className="font-poppins text-sm italic">Thu. May. 29th, 2025</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.2] 1st Pane Low-E Coating</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.lowECost / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.2] Gas Options</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.gasCost / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">[W#1.2] Screen Option</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">
                    ${(pricing.subtotals.bugScreenCost / 2).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-100 rounded">
                  <span className="font-poppins text-sm text-gray-800">Mullion Couplers</span>
                  <span className="font-poppins text-sm font-bold text-gray-800">$12.94</span>
                </div>

                {/* Enhanced pricing multipliers display */}
                <div className="border-t pt-3 mt-3">
                  <div className="text-xs text-gray-600 mb-2">Pricing Adjustments:</div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex justify-between">
                      <span>Size Factor:</span>
                      <span>{(pricing.sizeMultiplier * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Complexity:</span>
                      <span>{(pricing.complexityMultiplier * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Installation:</span>
                      <span>{(pricing.installationMultiplier * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Quality Tier:</span>
                      <span>{(pricing.qualityMultiplier * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2">Upgrades Selected: {pricing.upgradesCount}</div>
                </div>
              </div>

              {/* Add to Cart Section */}
              <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4 pt-6 mt-6 border-t border-gray-200">
                <Input
                  placeholder="Description"
                  className="flex-1 font-poppins text-sm"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
                <div className="flex items-center space-x-2">
                  <span className="font-poppins text-sm text-gray-600">Qty</span>
                  <Input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    className="w-16 text-center font-poppins text-sm"
                    min="1"
                  />
                </div>
                <Button
                  onClick={handleAddToCart}
                  className="bg-primary-orange hover:bg-orange-600 text-white font-bebas text-lg px-6 py-2 w-full sm:w-auto"
                >
                  Add To Cart
                </Button>
              </div>
            </div>
          </div>

          {/* Center Column - Configuration Options */}
          <div className="space-y-4 order-2">
            {/* Window Configuration Header */}
            <div className="bg-black text-white text-center py-3 rounded-lg">
              <h2 className="font-bebas text-xl">Window Configuration</h2>
            </div>

            {/* Quality Tier Selection */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <label className="font-poppins text-sm font-semibold text-gray-800">Quality Tier</label>
                <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">?</span>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 border-2 border-primary-orange rounded flex-shrink-0 bg-orange-50 flex items-center justify-center">
                  <div className="w-4 h-4 bg-primary-orange rounded-sm"></div>
                </div>
                <Select value={qualityTier} onValueChange={setQualityTier}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard Quality</SelectItem>
                    <SelectItem value="premium">Premium Quality (+15%)</SelectItem>
                    <SelectItem value="luxury">Luxury Quality (+35%)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Number of Vertical Windows */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <label className="font-poppins text-sm font-semibold text-gray-800">Number Of Vertical Windows</label>
                <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">?</span>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 border-2 border-gray-300 rounded flex-shrink-0 bg-gray-50 flex items-center justify-center">
                  <div className="w-4 h-4 border border-gray-400"></div>
                </div>
                <Select value={numberOfVerticalWindows.toString()}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Window(S) High</SelectItem>
                    <SelectItem value="2">2 Window(S) High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Number of Horizontal Windows */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <label className="font-poppins text-sm font-semibold text-gray-800">Number Of Horizontal Windows</label>
                <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">?</span>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 border-2 border-gray-300 rounded flex-shrink-0 bg-gray-50 flex items-center justify-center">
                  <div className="flex space-x-1">
                    <div className="w-2 h-4 border border-gray-400"></div>
                    <div className="w-2 h-4 border border-gray-400"></div>
                  </div>
                </div>
                <Select value={numberOfHorizontalWindows.toString()}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Window(S) Wide</SelectItem>
                    <SelectItem value="2">2 Window(S) Wide</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Click On A Window Below To Edit */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h3 className="font-poppins text-sm font-semibold text-gray-800 mb-3">Click On A Window Below To Edit</h3>
              <div className="grid grid-cols-2 gap-2">
                <button className="p-3 rounded text-center font-poppins text-sm font-semibold bg-primary-orange text-white">
                  W1.2
                  <br />
                  {currentFrameWidth}" X {currentFrameHeight}"
                  <br />
                  {windowType}
                </button>
                <button className="p-3 rounded text-center font-poppins text-sm font-semibold bg-black text-white">
                  W1.1
                  <br />
                  {currentFrameWidth}" X {currentFrameHeight}"
                  <br />
                  {windowType}
                </button>
              </div>
            </div>

            {/* Whole Opening Details */}
            <div
              className="bg-primary-orange text-white p-3 rounded-lg cursor-pointer"
              onClick={() => setWholeOpeningExpanded(!wholeOpeningExpanded)}
            >
              <div className="flex items-center justify-between">
                <h3 className="font-bebas text-lg">Whole Opening Details</h3>
                {wholeOpeningExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
            </div>

            {wholeOpeningExpanded && (
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="font-poppins text-sm font-semibold text-gray-800">Overall Window Depth: 3.250"</span>
                  <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">?</span>
                  </div>
                </div>
              </div>
            )}

            {/* Exterior Options */}
            <div
              className="bg-primary-orange text-white p-3 rounded-lg cursor-pointer"
              onClick={() => setExteriorOptionsExpanded(!exteriorOptionsExpanded)}
            >
              <div className="flex items-center justify-between">
                <h3 className="font-bebas text-lg">Exterior Options</h3>
                {exteriorOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
            </div>

            {exteriorOptionsExpanded && (
              <div className="space-y-4">
                {/* Exterior Finish Color */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Exterior Finish Color</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-8 h-8 border-2 border-gray-300 rounded ${
                        exteriorFinish === "white"
                          ? "bg-white"
                          : exteriorFinish === "black"
                            ? "bg-black"
                            : "bg-yellow-100"
                      }`}
                    ></div>
                    <Select value={exteriorFinish} onValueChange={setExteriorFinish}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="white">White</SelectItem>
                        <SelectItem value="black">Black (+18%)</SelectItem>
                        <SelectItem value="beige">Beige (+10%)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Do You Require A Brickmould? */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">
                      Do You Require A Brickmould?
                    </label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value={requireBrickmould} onValueChange={setRequireBrickmould}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="No">No</SelectItem>
                        <SelectItem value="Yes">Yes (+$35.50)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Snap-In Nailing Fin? */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Snap-In Nailing Fin?</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value={snapInNailingFin} onValueChange={setSnapInNailingFin}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="No">No</SelectItem>
                        <SelectItem value="Yes">Yes (+$26.75)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {/* Interior Options */}
            <div
              className="bg-primary-orange text-white p-3 rounded-lg cursor-pointer"
              onClick={() => setInteriorOptionsExpanded(!interiorOptionsExpanded)}
            >
              <div className="flex items-center justify-between">
                <h3 className="font-bebas text-lg">Interior Options</h3>
                {interiorOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
            </div>

            {interiorOptionsExpanded && (
              <div className="space-y-4">
                {/* Interior Finish Color */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Interior Finish Color</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-8 h-8 border-2 border-gray-300 rounded ${
                        interiorFinish === "white"
                          ? "bg-white"
                          : interiorFinish === "black"
                            ? "bg-black"
                            : "bg-yellow-100"
                      }`}
                    ></div>
                    <Select value={interiorFinish} onValueChange={setInteriorFinish}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="white">White</SelectItem>
                        <SelectItem value="black">Black (+$18.50)</SelectItem>
                        <SelectItem value="beige">Beige (+$14.25)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Do You Require Interior Jamb? */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">
                      Do You Require Interior Jamb?
                    </label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value="No">
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="No">No</SelectItem>
                        <SelectItem value="Yes">Yes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Interior Returns */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Interior Returns</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value="No">
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="No">No</SelectItem>
                        <SelectItem value="Yes">Yes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}

            {/* Glass Options */}
            <div
              className="bg-primary-orange text-white p-3 rounded-lg cursor-pointer"
              onClick={() => setGlassOptionsExpanded(!glassOptionsExpanded)}
            >
              <div className="flex items-center justify-between">
                <h3 className="font-bebas text-lg">Glass Options</h3>
                {glassOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
            </div>

            {glassOptionsExpanded && (
              <div className="space-y-4">
                {/* Choose Glazing Type */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Choose Glazing Type</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-50 border border-blue-300 rounded flex items-center justify-center">
                      <svg viewBox="0 0 20 20" className="w-4 h-4 text-blue-500">
                        <rect x="2" y="2" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" />
                        <rect x="6" y="6" width="8" height="8" fill="none" stroke="currentColor" strokeWidth="1" />
                      </svg>
                    </div>
                    <Select value={glazingType} onValueChange={setGlazingType}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Double Pane Glass">Double Pane Glass</SelectItem>
                        <SelectItem value="Triple Pane Glass">Triple Pane Glass (+$25.03)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 1st Pane Low-E Coating */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">1st Pane Low-E Coating</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-8 bg-blue-100 border border-blue-300 rounded flex items-center justify-center">
                      <span className="text-xs text-blue-600 font-bold">CG</span>
                    </div>
                    <Select value={lowECoating1st} onValueChange={setLowECoating1st}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ClimaGuard 80/70 (Single)">ClimaGuard 80/70 (+$28.50)</SelectItem>
                        <SelectItem value="None">None</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 2nd Pane Low-E Coating */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">2nd Pane Low-E Coating</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value={lowECoating2nd} onValueChange={setLowECoating2nd}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="None">None</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Select Gas Type */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Select Gas Type</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-50 border border-blue-300 rounded flex items-center justify-center">
                      <svg viewBox="0 0 20 20" className="w-4 h-4 text-blue-500">
                        <path
                          d="M2 10c0 0 4-8 8-8s8 8 8 8-4 8-8 8-8-8-8-8z"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    </div>
                    <Select value={gasType} onValueChange={setGasType}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Argon">Argon (+$18.75)</SelectItem>
                        <SelectItem value="Air">Air</SelectItem>
                        <SelectItem value="Krypton">Krypton (+$48.00)</SelectItem>
                        <SelectItem value="Xenon">Xenon (+$72.50)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Do You Require Tint Or Frosting? */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">
                      Do You Require Tint Or Frosting?
                    </label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value={requireTintFrosting} onValueChange={setRequireTintFrosting}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="None">None</SelectItem>
                        <SelectItem value="Tint">Tint (+$32.00)</SelectItem>
                        <SelectItem value="Frosting">Frosting (+$42.50)</SelectItem>
                        <SelectItem value="Decorative">Decorative (+$58.00)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Do You Require Security Glass? */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">
                      Do You Require Security Glass?
                    </label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value={requireSecurityGlass} onValueChange={setRequireSecurityGlass}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="None">None</SelectItem>
                        <SelectItem value="Tempered">Tempered (+$52.75)</SelectItem>
                        <SelectItem value="Laminated">Laminated (+$78.50)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Spacer Type */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Spacer Type</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded flex items-center justify-center">
                      <div className="w-4 h-2 bg-gray-400 rounded-sm"></div>
                    </div>
                    <Select value={spacerType} onValueChange={setSpacerType}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Endur® Warm-Edge Spacer">Endur® Warm-Edge Spacer (+$22.25)</SelectItem>
                        <SelectItem value="Standard Spacer">Standard Spacer</SelectItem>
                        <SelectItem value="Super Spacer">Super Spacer (+$35.00)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Window Specific Options */}
          <div className="space-y-4 order-3">
            {/* Window #1.1 Specific Options */}
            <div
              className="bg-primary-orange text-white p-3 rounded-lg cursor-pointer"
              onClick={() => setSpecificOptionsExpanded(!specificOptionsExpanded)}
            >
              <div className="flex items-center justify-between">
                <h3 className="font-bebas text-lg">Window #1.1 Specific Options</h3>
                {specificOptionsExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
              </div>
            </div>

            {specificOptionsExpanded && (
              <div className="space-y-4">
                {/* Window Selection Dropdown */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <Select
                    value={`W11 - ${windowType} - ${currentFrameWidth.toFixed(3)}x${currentFrameHeight.toFixed(3)} - $${pricing.totalPrice.toFixed(2)}`}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        value={`W11 - ${windowType} - ${currentFrameWidth.toFixed(3)}x${currentFrameHeight.toFixed(3)} - $${pricing.totalPrice.toFixed(2)}`}
                      >
                        W11 - {windowType} - {currentFrameWidth.toFixed(3)}x{currentFrameHeight.toFixed(3)} - $
                        {pricing.totalPrice.toFixed(2)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Enhanced W1.1 Energy Ratings */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-poppins text-sm font-semibold text-gray-800">W1.1 Energy Ratings</h4>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    <div className="text-center p-3 border border-gray-200 rounded bg-gradient-to-b from-green-50 to-green-100">
                      <div className="font-poppins text-xs font-semibold text-gray-600">ER</div>
                      <div className="font-poppins text-2xl font-bold text-green-700">{energyRatings.er}</div>
                    </div>
                    <div className="text-center p-3 border border-gray-200 rounded bg-gradient-to-b from-blue-50 to-blue-100">
                      <div className="font-poppins text-xs font-semibold text-gray-600">SHGC</div>
                      <div className="font-poppins text-2xl font-bold text-blue-700">{energyRatings.shgc}</div>
                    </div>
                    <div className="text-center p-3 border border-gray-200 rounded bg-gradient-to-b from-yellow-50 to-yellow-100">
                      <div className="font-poppins text-xs font-semibold text-gray-600">VT</div>
                      <div className="font-poppins text-2xl font-bold text-yellow-700">{energyRatings.vt}</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="text-center p-3 border border-gray-200 rounded bg-gradient-to-b from-purple-50 to-purple-100">
                      <div className="font-poppins text-xs font-semibold text-gray-600">U-Factor (I-P)</div>
                      <div className="font-poppins text-2xl font-bold text-purple-700">{energyRatings.uFactorIP}</div>
                    </div>
                    <div className="text-center p-3 border border-gray-200 rounded bg-gradient-to-b from-red-50 to-red-100">
                      <div className="font-poppins text-xs font-semibold text-gray-600">U-Factor (SI)</div>
                      <div className="font-poppins text-2xl font-bold text-red-700">{energyRatings.uFactorSI}</div>
                    </div>
                  </div>
                  <div className="mt-3 text-center">
                    <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800">
                      {qualityTier.toUpperCase()} QUALITY TIER
                    </div>
                  </div>
                </div>

                {/* Enhanced NRCan Model # */}
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg border">
                  <div className="mb-2">
                    <span className="font-poppins text-xs font-semibold text-gray-600">NRCan Model #</span>
                  </div>
                  <div className="font-poppins text-sm text-gray-800 mb-4 font-mono bg-white p-2 rounded border">
                    PWM-CA-3,CL-3,8071(3)-16AR97SP
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="font-poppins text-xs font-semibold text-gray-600">Most Efficient 2025</div>
                      <div className="font-poppins text-sm text-gray-800 font-semibold">
                        {energyRatings.er >= 35 ? "Y" : "N"}
                      </div>
                    </div>
                    <div>
                      <div className="font-poppins text-xs font-semibold text-gray-600">NRCan Reference #</div>
                      <div className="font-poppins text-sm text-gray-800 font-mono">NR10905-35751797-ESS</div>
                    </div>
                  </div>
                </div>

                {/* Meets Egress */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Meets Egress</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <Button className="w-full bg-green-600 hover:bg-green-700 text-white font-poppins">
                    Yes - Compliant
                  </Button>
                </div>

                {/* Window Type */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Window Type</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary-orange border border-primary-orange rounded flex items-center justify-center">
                      <div className="w-4 h-4 bg-white rounded-sm"></div>
                    </div>
                    <Select value={windowType} onValueChange={setWindowType}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Awning Window">Awning Window</SelectItem>
                        <SelectItem value="Casement Window">Casement Window (+$22.75)</SelectItem>
                        <SelectItem value="Double Hung">Double Hung (+$28.50)</SelectItem>
                        <SelectItem value="Sliding">Sliding (+$18.25)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Enhanced Window Dimensions */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-poppins text-sm font-semibold text-gray-800 mb-3">Window Dimensions</h4>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="font-poppins text-xs text-gray-600">Min. Width</div>
                      <div className="font-poppins text-sm font-semibold text-green-600">14.250"</div>
                    </div>
                    <div>
                      <div className="font-poppins text-xs text-gray-600">Max Width</div>
                      <div className="font-poppins text-sm font-semibold text-red-600">36.000"</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="font-poppins text-xs text-gray-600">Min. Height</div>
                      <div className="font-poppins text-sm font-semibold text-green-600">13.000"</div>
                    </div>
                    <div>
                      <div className="font-poppins text-xs text-gray-600">Max Height</div>
                      <div className="font-poppins text-sm font-semibold text-red-600">90.000"</div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="font-poppins text-xs font-semibold text-gray-600 mb-1 block">
                        Width (inches)
                      </label>
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          value={currentFrameWidth}
                          onChange={(e) => setCurrentFrameWidth(Number(e.target.value))}
                          className="flex-1 text-center font-poppins text-lg font-bold"
                          min="14.25"
                          max="36"
                          step="0.125"
                        />
                        <span className="font-poppins text-sm text-gray-600">inches</span>
                      </div>
                    </div>
                    <div>
                      <label className="font-poppins text-xs font-semibold text-gray-600 mb-1 block">
                        Height (inches)
                      </label>
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          value={currentFrameHeight}
                          onChange={(e) => setCurrentFrameHeight(Number(e.target.value))}
                          className="flex-1 text-center font-poppins text-lg font-bold"
                          min="13"
                          max="90"
                          step="0.125"
                        />
                        <span className="font-poppins text-sm text-gray-600">inches</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
                    Area: {pricing.areaSqFt.toFixed(2)} sq ft | Price per sq ft: ${pricing.pricePerSqFt.toFixed(2)}
                  </div>
                </div>

                {/* Hardware Type */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Hardware Type</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-8 bg-red-100 border border-red-300 rounded flex items-center justify-center">
                      <span className="text-xs text-red-600 font-bold">Roto</span>
                    </div>
                    <Select value={hardwareType} onValueChange={setHardwareType}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Roto Classic">Roto Classic</SelectItem>
                        <SelectItem value="Roto Premium">Roto Premium (+$32.50)</SelectItem>
                        <SelectItem value="Roto Luxury">Roto Luxury (+$58.00)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Hardware Colour */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Hardware Colour</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-8 h-8 border border-gray-300 rounded ${
                        hardwareColour === "White"
                          ? "bg-white"
                          : hardwareColour === "Black"
                            ? "bg-black"
                            : "bg-yellow-600"
                      }`}
                    ></div>
                    <Select value={hardwareColour} onValueChange={setHardwareColour}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="White">White</SelectItem>
                        <SelectItem value="Black">Black (+$10.50)</SelectItem>
                        <SelectItem value="Bronze">Bronze (+$15.50)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Roto Corner Lock */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Roto Corner Lock</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value={rotoCornerLock} onValueChange={setRotoCornerLock}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="NO">NO</SelectItem>
                        <SelectItem value="YES">YES (+$28.75)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Opening Direction (Ext. View) */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">
                      Opening Direction (Ext. View)
                    </label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-50 border border-blue-300 rounded flex items-center justify-center">
                      <div className="text-xs text-blue-600">→</div>
                    </div>
                    <Select value="Right">
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Right">Right</SelectItem>
                        <SelectItem value="Left">Left</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Enhanced Type Of Bug Screen */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Type Of Bug Screen</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                    <Select value={bugScreenType} onValueChange={setBugScreenType}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Regular Screen">Regular Screen (+$22.50)</SelectItem>
                        <SelectItem value="Pet Screen">Pet Screen (+$34.75)</SelectItem>
                        <SelectItem value="Solar Screen">Solar Screen (+$48.25)</SelectItem>
                        <SelectItem value="Retractable">Retractable (+$85.00)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Egress Hardware */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Egress Hardware</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-orange-50 border border-gray-300 rounded flex items-center justify-center">
                      <div className="text-xs text-gray-600">😊</div>
                    </div>
                    <Select value="NO">
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="NO">NO</SelectItem>
                        <SelectItem value="YES">YES</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Enhanced Special Glazing Options */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Special Glazing Options</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                    <Select value={specialGlazing} onValueChange={setSpecialGlazing}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Default Glazing">Default Glazing</SelectItem>
                        <SelectItem value="Custom Glazing">Custom Glazing (+$65.00)</SelectItem>
                        <SelectItem value="Smart Glass">Smart Glass (+$185.00)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Enhanced Do You Require Grills? */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Do You Require Grills?</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                    <Select value={requireGrills} onValueChange={setRequireGrills}>
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="NO">NO</SelectItem>
                        <SelectItem value="YES">YES (+$42.50)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Apply Grills For */}
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <div className="flex items-center justify-between mb-3">
                    <label className="font-poppins text-sm font-semibold text-gray-800">Apply Grills For</label>
                    <div className="w-6 h-6 bg-primary-orange rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 border border-gray-300 rounded"></div>
                    <Select value="All Windows">
                      <SelectTrigger className="flex-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="All Windows">All Windows</SelectItem>
                        <SelectItem value="Selected Windows">Selected Windows</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
