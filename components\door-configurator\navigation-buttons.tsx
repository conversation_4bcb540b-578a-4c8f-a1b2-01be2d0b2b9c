"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore } from "@/lib/door-configurator-store"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

export function NavigationButtons() {
  const { currentStep, setCurrentStep, canProceedToNext, getNextStep, getPreviousStep } = useDoorConfiguratorStore()

  const nextStep = getNextStep()
  const previousStep = getPreviousStep()
  const canProceed = canProceedToNext()

  const handleNext = () => {
    if (nextStep && canProceed) {
      setCurrentStep(nextStep)
    }
  }

  const handlePrevious = () => {
    if (previousStep) {
      setCurrentStep(previousStep)
    }
  }

  return (
    <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={!previousStep}
          className="flex items-center bg-transparent"
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>
      </motion.div>

      <div className="text-sm text-gray-500">
        Step{" "}
        {currentStep === "configuration"
          ? 1
          : currentStep === "door"
            ? 2
            : currentStep === "doorglass"
              ? 3
              : currentStep === "colors"
                ? 4
                : currentStep === "handle"
                  ? 5
                  : 6}{" "}
        of 6
      </div>

      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
        <Button
          onClick={handleNext}
          disabled={!nextStep || !canProceed}
          className="flex items-center bg-orange-500 hover:bg-orange-600"
        >
          {currentStep === "summary" ? "Complete" : "Next"}
          {currentStep !== "summary" && <ChevronRight className="w-4 h-4 ml-2" />}
        </Button>
      </motion.div>
    </div>
  )
}
