"use client"

import { Canvas } from "@react-three/fiber"
import { OrbitControls, Environment, Html, Center, Text } from "@react-three/drei"
import { Suspense, useRef, forwardRef, useImperativeHandle, useState, useEffect } from "react"

interface WindowModel3DProps {
  glazingType: string
  gasType: string
  hardwareColour: string
  requireGrills: string
  frameWidth: number
  frameHeight: number
  wireframeMode: boolean
  interiorFinish: string
  exteriorFinish: string
  showMeasurements: boolean
}

function Loader() {
  return (
    <Html center>
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-primary-orange border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <div className="font-poppins text-sm text-gray-600">Loading 3D Model...</div>
      </div>
    </Html>
  )
}

// Enhanced Measurement Lines Component
const MeasurementLines = forwardRef<any, { frameWidth: number; frameHeight: number; showMeasurements: boolean }>(
  ({ frameWidth, frameHeight, showMeasurements }, ref) => {
    if (!showMeasurements) return null

    // Convert inches to 3D units (scale factor)
    const scaleX = frameWidth / 24 // Base width 24 inches
    const scaleY = frameHeight / 36 // Base height 36 inches

    // Calculate positions to stick close to the model with minimal spacing
    const modelWidth = 2 * scaleX
    const modelHeight = 3 * scaleY
    const spacing = 0.15 // Minimal spacing from model

    return (
      <group ref={ref}>
        {/* Horizontal measurement line (bottom) - closer to model */}
        <group position={[0, -(modelHeight / 2) - spacing, 0.3]}>
          {/* Main line */}
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[modelWidth + 0.2, 0.015, 0.015]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          {/* Left end cap */}
          <mesh position={[-(modelWidth + 0.2) / 2, 0, 0]}>
            <boxGeometry args={[0.015, 0.08, 0.015]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          {/* Right end cap */}
          <mesh position={[(modelWidth + 0.2) / 2, 0, 0]}>
            <boxGeometry args={[0.015, 0.08, 0.015]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          {/* Measurement text with better styling */}
          <Text
            position={[0, -0.25, 0]}
            fontSize={0.18}
            color="#FF6B35"
            anchorX="center"
            anchorY="middle"
            font="/fonts/Inter-Bold.ttf"
          >
            {frameWidth.toFixed(3)}"
          </Text>
          {/* Background for text readability */}
          <mesh position={[0, -0.25, -0.01]}>
            <planeGeometry args={[1.2, 0.3]} />
            <meshBasicMaterial color="#ffffff" opacity={0.9} transparent />
          </mesh>
        </group>

        {/* Vertical measurement line (left) - closer to model */}
        <group position={[-(modelWidth / 2) - spacing, 0, 0.3]}>
          {/* Main line */}
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[0.015, modelHeight + 0.2, 0.015]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          {/* Top end cap */}
          <mesh position={[0, (modelHeight + 0.2) / 2, 0]}>
            <boxGeometry args={[0.08, 0.015, 0.015]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          {/* Bottom end cap */}
          <mesh position={[0, -(modelHeight + 0.2) / 2, 0]}>
            <boxGeometry args={[0.08, 0.015, 0.015]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          {/* Measurement text with better styling */}
          <Text
            position={[-0.35, 0, 0]}
            fontSize={0.18}
            color="#FF6B35"
            anchorX="center"
            anchorY="middle"
            rotation={[0, 0, Math.PI / 2]}
            font="/fonts/Inter-Bold.ttf"
          >
            {frameHeight.toFixed(3)}"
          </Text>
          {/* Background for text readability */}
          <mesh position={[-0.35, 0, -0.01]} rotation={[0, 0, Math.PI / 2]}>
            <planeGeometry args={[1.2, 0.3]} />
            <meshBasicMaterial color="#ffffff" opacity={0.9} transparent />
          </mesh>
        </group>

        {/* Enhanced Window label with better positioning */}
        <group position={[-(modelWidth / 2) + 0.3, modelHeight / 2 - 0.3, 0.4]}>
          {/* Label background */}
          <mesh position={[0, 0, -0.01]}>
            <planeGeometry args={[0.8, 0.4]} />
            <meshBasicMaterial color="#FF6B35" opacity={0.95} transparent />
          </mesh>
          {/* Label text */}
          <Text
            position={[0, 0, 0]}
            fontSize={0.25}
            color="#ffffff"
            anchorX="center"
            anchorY="middle"
            font="/fonts/Inter-Bold.ttf"
          >
            W1.1
          </Text>
        </group>

        {/* Additional measurement indicators */}
        <group position={[0, 0, 0.35]}>
          {/* Corner indicators */}
          <mesh position={[modelWidth / 2, modelHeight / 2, 0]}>
            <sphereGeometry args={[0.03]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          <mesh position={[-modelWidth / 2, modelHeight / 2, 0]}>
            <sphereGeometry args={[0.03]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          <mesh position={[modelWidth / 2, -modelHeight / 2, 0]}>
            <sphereGeometry args={[0.03]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
          <mesh position={[-modelWidth / 2, -modelHeight / 2, 0]}>
            <sphereGeometry args={[0.03]} />
            <meshBasicMaterial color="#FF6B35" />
          </mesh>
        </group>
      </group>
    )
  },
)

MeasurementLines.displayName = "MeasurementLines"

// Enhanced Window Model Component
const WindowModel = forwardRef<any, WindowModel3DProps>(
  (
    {
      glazingType,
      gasType,
      hardwareColour,
      requireGrills,
      frameWidth,
      frameHeight,
      wireframeMode,
      interiorFinish,
      exteriorFinish,
      showMeasurements,
    },
    ref,
  ) => {
    // Calculate scale based on frame dimensions
    const baseWidth = 24 // Base width in inches
    const baseHeight = 36 // Base height in inches
    const scaleX = frameWidth / baseWidth
    const scaleY = frameHeight / baseHeight
    const scaleZ = Math.min(scaleX, scaleY) // Maintain proportions

    // Enhanced model placeholders for different colors
    const getModelPlaceholder = (color: string) => {
      const baseColor = color === "white" ? "#f8f9fa" : color === "black" ? "#1a1a1a" : "#d4c29b" // beige
      const frameColor = color === "white" ? "#e9ecef" : color === "black" ? "#343a40" : "#c4a484"

      return (
        <group>
          {/* Main window frame */}
          <mesh position={[0, 0, 0]} castShadow receiveShadow>
            <boxGeometry args={[2, 3, 0.2]} />
            <meshStandardMaterial
              color={baseColor}
              wireframe={wireframeMode}
              roughness={0.2}
              metalness={0.1}
              envMapIntensity={0.5}
            />
          </mesh>

          {/* Inner frame detail */}
          <mesh position={[0, 0, 0.05]} castShadow>
            <boxGeometry args={[1.9, 2.9, 0.1]} />
            <meshStandardMaterial color={frameColor} wireframe={wireframeMode} roughness={0.3} metalness={0.05} />
          </mesh>

          {/* Glass area with enhanced realism */}
          <mesh position={[0, 0, 0.11]} receiveShadow>
            <planeGeometry args={[1.6, 2.6]} />
            <meshPhysicalMaterial
              color={glazingType === "Triple Pane Glass" ? "#b3d9ff" : "#87CEEB"}
              transparent={true}
              opacity={glazingType === "Triple Pane Glass" ? 0.25 : 0.3}
              transmission={0.8}
              roughness={0.02}
              metalness={0}
              clearcoat={1.0}
              clearcoatRoughness={0.1}
              wireframe={wireframeMode}
              envMapIntensity={1.2}
            />
          </mesh>

          {/* Hardware representation */}
          <mesh position={[0.7, 0, 0.12]} castShadow>
            <cylinderGeometry args={[0.03, 0.03, 0.1]} />
            <meshStandardMaterial
              color={hardwareColour === "White" ? "#ffffff" : hardwareColour === "Black" ? "#000000" : "#8B4513"}
              metalness={0.8}
              roughness={0.2}
            />
          </mesh>

          {/* Grills if required */}
          {requireGrills === "YES" && (
            <group position={[0, 0, 0.13]}>
              {/* Horizontal grills */}
              <mesh position={[0, 0.6, 0]}>
                <boxGeometry args={[1.5, 0.02, 0.02]} />
                <meshStandardMaterial color="#666666" />
              </mesh>
              <mesh position={[0, 0, 0]}>
                <boxGeometry args={[1.5, 0.02, 0.02]} />
                <meshStandardMaterial color="#666666" />
              </mesh>
              <mesh position={[0, -0.6, 0]}>
                <boxGeometry args={[1.5, 0.02, 0.02]} />
                <meshStandardMaterial color="#666666" />
              </mesh>
              {/* Vertical grills */}
              <mesh position={[0.5, 0, 0]}>
                <boxGeometry args={[0.02, 2.5, 0.02]} />
                <meshStandardMaterial color="#666666" />
              </mesh>
              <mesh position={[-0.5, 0, 0]}>
                <boxGeometry args={[0.02, 2.5, 0.02]} />
                <meshStandardMaterial color="#666666" />
              </mesh>
            </group>
          )}

          {/* Model identifier text */}
          <Html position={[0, -2.2, 0]} center>
            <div className="bg-white px-3 py-2 rounded-lg shadow-lg text-xs font-semibold text-gray-700 border border-gray-200">
              <div className="text-center">
                <div className="font-bold text-primary-orange">{color.toUpperCase()} MODEL</div>
                <div className="text-gray-500 text-xs mt-1">
                  {glazingType} • {gasType} • {hardwareColour}
                </div>
              </div>
            </div>
          </Html>
        </group>
      )
    }

    // Select model based on exterior finish
    const getSelectedModel = () => {
      switch (exteriorFinish) {
        case "white":
          return getModelPlaceholder("white")
        case "black":
          return getModelPlaceholder("black")
        case "beige":
          return getModelPlaceholder("beige")
        default:
          return getModelPlaceholder("white")
      }
    }

    return (
      <group ref={ref} scale={[scaleX, scaleY, scaleZ]} position={[0, 0, 0]}>
        {getSelectedModel()}
        <MeasurementLines frameWidth={frameWidth} frameHeight={frameHeight} showMeasurements={showMeasurements} />
      </group>
    )
  },
)

WindowModel.displayName = "WindowModel"

export interface Window3DViewerRef {
  zoomIn: () => void
  zoomOut: () => void
  panLeft: () => void
  panRight: () => void
  panUp: () => void
  panDown: () => void
  resetView: () => void
  toggleWireframe: () => void
  toggleMeasurements: () => void
  rotateLeft: () => void
  rotateRight: () => void
  rotateUp: () => void
  rotateDown: () => void
}

interface Window3DViewerProps {
  glazingType: string
  gasType: string
  hardwareColour: string
  requireGrills: string
  frameWidth: number
  frameHeight: number
  interiorFinish: string
  exteriorFinish: string
  showMeasurements: boolean
  isFullScreen: boolean
}

const Window3DViewer = forwardRef<Window3DViewerRef, Window3DViewerProps>(
  (
    {
      glazingType,
      gasType,
      hardwareColour,
      requireGrills,
      frameWidth,
      frameHeight,
      interiorFinish,
      exteriorFinish,
      showMeasurements,
      isFullScreen,
    },
    ref,
  ) => {
    const controlsRef = useRef<any>(null)
    const windowModelRef = useRef<any>(null)
    const [wireframeMode, setWireframeMode] = useState(false)
    const [modelRotation, setModelRotation] = useState({ x: 0, y: 0, z: 0 })
    const [isChangingModel, setIsChangingModel] = useState(false)
    const [currentExteriorFinish, setCurrentExteriorFinish] = useState(exteriorFinish)
    const [pendingExteriorFinish, setPendingExteriorFinish] = useState<string | null>(null)
    const [modelKey, setModelKey] = useState(0)
    const [internalShowMeasurements, setInternalShowMeasurements] = useState(showMeasurements)

    const rotationStep = Math.PI / 12 // Smoother rotation steps
    const panStep = 0.3
    const zoomStep = 0.3

    // Handle exterior finish changes with loading simulation
    useEffect(() => {
      const supportedColors = ["white", "black", "beige"]
      const newColor = supportedColors.includes(exteriorFinish) ? exteriorFinish : "white"

      if (newColor !== currentExteriorFinish) {
        const timer = setTimeout(() => {
          setPendingExteriorFinish(newColor)
          setIsChangingModel(true)

          const loadTimer = setTimeout(() => {
            setCurrentExteriorFinish(newColor)
            setPendingExteriorFinish(null)
            setIsChangingModel(false)
            setModelKey((prev) => prev + 1)
          }, 1500)

          return () => clearTimeout(loadTimer)
        }, 0)

        return () => clearTimeout(timer)
      }
    }, [exteriorFinish, currentExteriorFinish])

    useEffect(() => {
      setInternalShowMeasurements(showMeasurements)
    }, [showMeasurements])

    useEffect(() => {
      if (controlsRef.current) {
        const camera = controlsRef.current.object
        camera.position.set(0, 0, 4)
        camera.lookAt(0, 0, 0)
        controlsRef.current.target.set(0, 0, 0)
        controlsRef.current.update()
      }
    }, [])

    useImperativeHandle(ref, () => ({
      zoomIn: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          const target = controlsRef.current.target
          const direction = camera.position.clone().sub(target).normalize()
          camera.position.add(direction.multiplyScalar(-zoomStep))
          const distance = camera.position.distanceTo(target)
          if (distance < 1.5) {
            camera.position.copy(target).add(direction.multiplyScalar(1.5))
          }
          controlsRef.current.update()
        }
      },
      zoomOut: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          const target = controlsRef.current.target
          const direction = camera.position.clone().sub(target).normalize()
          camera.position.add(direction.multiplyScalar(zoomStep))
          const distance = camera.position.distanceTo(target)
          if (distance > 8) {
            camera.position.copy(target).add(direction.multiplyScalar(8))
          }
          controlsRef.current.update()
        }
      },
      panLeft: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          const target = controlsRef.current.target
          const right = camera.up.clone().cross(camera.position.clone().sub(target).normalize()).normalize()
          const panDirection = right.multiplyScalar(-panStep)
          camera.position.add(panDirection)
          target.add(panDirection)
          controlsRef.current.update()
        }
      },
      panRight: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          const target = controlsRef.current.target
          const right = camera.up.clone().cross(camera.position.clone().sub(target).normalize()).normalize()
          const panDirection = right.multiplyScalar(panStep)
          camera.position.add(panDirection)
          target.add(panDirection)
          controlsRef.current.update()
        }
      },
      panUp: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          const target = controlsRef.current.target
          const panDirection = camera.up.clone().normalize().multiplyScalar(panStep)
          camera.position.add(panDirection)
          target.add(panDirection)
          controlsRef.current.update()
        }
      },
      panDown: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          const target = controlsRef.current.target
          const panDirection = camera.up.clone().normalize().multiplyScalar(-panStep)
          camera.position.add(panDirection)
          target.add(panDirection)
          controlsRef.current.update()
        }
      },
      resetView: () => {
        if (controlsRef.current) {
          const camera = controlsRef.current.object
          camera.position.set(0, 0, 4)
          camera.lookAt(0, 0, 0)
          controlsRef.current.target.set(0, 0, 0)
          controlsRef.current.update()
        }
        if (windowModelRef.current) {
          windowModelRef.current.rotation.set(0, 0, 0)
          setModelRotation({ x: 0, y: 0, z: 0 })
        }
      },
      toggleWireframe: () => {
        setWireframeMode((prev) => !prev)
      },
      toggleMeasurements: () => {
        setInternalShowMeasurements((prev) => !prev)
      },
      // Changed to rotate the model instead of panning camera
      rotateLeft: () => {
        if (windowModelRef.current) {
          const newY = modelRotation.y + rotationStep
          windowModelRef.current.rotation.y = newY
          setModelRotation((prev) => ({ ...prev, y: newY }))
        }
      },
      rotateRight: () => {
        if (windowModelRef.current) {
          const newY = modelRotation.y - rotationStep
          windowModelRef.current.rotation.y = newY
          setModelRotation((prev) => ({ ...prev, y: newY }))
        }
      },
      rotateUp: () => {
        if (windowModelRef.current) {
          const newX = modelRotation.x - rotationStep
          windowModelRef.current.rotation.x = newX
          setModelRotation((prev) => ({ ...prev, x: newX }))
        }
      },
      rotateDown: () => {
        if (windowModelRef.current) {
          const newX = modelRotation.x + rotationStep
          windowModelRef.current.rotation.x = newX
          setModelRotation((prev) => ({ ...prev, x: newX }))
        }
      },
    }))

    return (
      <div
        className={`w-full h-full bg-gradient-to-br from-gray-50 to-gray-200 rounded-lg overflow-hidden relative shadow-inner ${isFullScreen ? "rounded-none" : ""}`}
      >
        {/* Loading overlay during model change */}
        {isChangingModel && (
          <div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-white bg-opacity-90 transition-opacity">
            <div className="w-16 h-16 border-4 border-primary-orange border-t-transparent rounded-full animate-spin mb-2"></div>
            <div className="font-poppins text-sm text-gray-600">Loading {pendingExteriorFinish} model...</div>
            <div className="font-poppins text-xs text-gray-500 mt-1">
              Future: Will load /models/window-{pendingExteriorFinish}.glb
            </div>
          </div>
        )}

        <Canvas
          key={modelKey}
          camera={{
            position: [0, 0, 4],
            fov: 50,
            near: 0.1,
            far: 100,
          }}
          style={{ width: "100%", height: "100%" }}
          gl={{
            antialias: true,
            alpha: true,
            powerPreference: "high-performance",
          }}
          shadows
        >
          <Suspense fallback={<Loader />}>
            {/* Enhanced lighting setup */}
            <ambientLight intensity={0.5} color="#ffffff" />
            <directionalLight
              position={[10, 10, 5]}
              intensity={1.8}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
              shadow-camera-far={50}
              shadow-camera-left={-10}
              shadow-camera-right={10}
              shadow-camera-top={10}
              shadow-camera-bottom={-10}
              color="#ffffff"
            />
            <directionalLight position={[-8, 8, 8]} intensity={1.0} color="#e6f3ff" />
            <pointLight position={[0, 6, 0]} intensity={0.8} color="#ffffff" />
            <spotLight position={[6, 6, 6]} angle={0.4} penumbra={0.3} intensity={1.2} castShadow color="#ffffff" />

            <Center>
              <WindowModel
                ref={windowModelRef}
                glazingType={glazingType}
                gasType={gasType}
                hardwareColour={hardwareColour}
                requireGrills={requireGrills}
                frameWidth={frameWidth}
                frameHeight={frameHeight}
                wireframeMode={wireframeMode}
                interiorFinish={interiorFinish}
                exteriorFinish={currentExteriorFinish}
                showMeasurements={internalShowMeasurements}
              />
            </Center>

            {/* Enhanced environment */}
            <Environment preset="studio" background={false} blur={0.05} />

            <OrbitControls
              ref={controlsRef}
              enablePan={true}
              enableRotate={true}
              enableZoom={true}
              enableDamping={true}
              dampingFactor={0.08}
              minDistance={1.5}
              maxDistance={8}
              maxPolarAngle={Math.PI / 1.6}
              minPolarAngle={Math.PI / 8}
              autoRotate={false}
              target={[0, 0, 0]}
            />
          </Suspense>
        </Canvas>

        {/* Model info overlay */}
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
          Current: {currentExteriorFinish.toUpperCase()} MODEL
        </div>

        {/* Measurements toggle indicator */}
        {internalShowMeasurements && (
          <div className="absolute top-2 left-2 bg-primary-orange text-white px-2 py-1 rounded text-xs">
            Measurements: ON
          </div>
        )}

        {/* Rotation indicator */}
        <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
          Rotation: X:{((modelRotation.x * 180) / Math.PI).toFixed(0)}° Y:
          {((modelRotation.y * 180) / Math.PI).toFixed(0)}°
        </div>

        {/* Subtle overlay for depth */}
        <div className="absolute inset-0 pointer-events-none bg-gradient-to-t from-black/5 to-transparent" />
      </div>
    )
  },
)

Window3DViewer.displayName = "Window3DViewer"

export default Window3DViewer
