"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore, type DoorConfiguration } from "@/lib/door-configurator-store"
import { Search, ChevronDown } from "lucide-react"
import { useState } from "react"

const extendedConfigurations = [
  { key: "standard", label: "Standard Door", description: "Basic door configuration" },
  { key: "premium", label: "Double Doors", description: "Two door configuration" },
  { key: "custom", label: "Single Door With Right Sidelite", description: "Door with right side panel" },
  { key: "single-left-sidelite", label: "Single Door With Left Sidelite", description: "Door with left side panel" },
  { key: "single-two-sidelites", label: "Single Door With Two Sidelites", description: "Door with both side panels" },
  { key: "single-transom", label: "Single Door With Transom", description: "Door with top window" },
  {
    key: "single-left-sidelite-transom",
    label: "Single Door With Left Sidelite and Transom",
    description: "Door with left panel and top window",
  },
  {
    key: "single-right-sidelite-transom",
    label: "Single Door With Right Sidelite and Transom",
    description: "Door with right panel and top window",
  },
  {
    key: "double-left-sidelite",
    label: "Double Doors With Left Sidelite",
    description: "Double doors with left panel",
  },
  {
    key: "double-right-sidelite",
    label: "Double Doors With Right Sidelite",
    description: "Double doors with right panel",
  },
  {
    key: "double-two-sidelites",
    label: "Double Doors With Two Sidelites",
    description: "Double doors with both panels",
  },
  {
    key: "single-right-large-sidelite",
    label: "Single Door With Right Large Sidelite",
    description: "Door with large right panel",
  },
  {
    key: "single-left-large-sidelite",
    label: "Single Door With Left Large Sidelite",
    description: "Door with large left panel",
  },
  {
    key: "single-two-large-sidelites",
    label: "Single Door With Two Large Sidelites",
    description: "Door with large side panels",
  },
]

export function ConfigurationStep() {
  const { configuration, setConfiguration } = useDoorConfiguratorStore()
  const [searchQuery, setSearchQuery] = useState("")

  const filteredConfigurations = extendedConfigurations.filter((config) =>
    config.label.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const renderConfigPreview = (configKey: string) => {
    const baseClasses = "w-full h-28 rounded border-2 border-gray-300 relative overflow-hidden bg-white"

    switch (configKey) {
      case "standard":
        return (
          <div className={`${baseClasses} bg-orange-500`}>
            <div className="w-full h-full bg-white m-1 rounded-sm flex items-center justify-center">
              <div className="w-1 h-8 bg-gray-800 rounded-full" />
            </div>
          </div>
        )
      case "premium":
        return (
          <div className={`${baseClasses} flex gap-px`}>
            <div className="flex-1 bg-white flex items-center justify-center border-r border-gray-300">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
          </div>
        )
      case "custom":
      case "single-right-sidelite":
        return (
          <div className={`${baseClasses} flex gap-px`}>
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
            <div className="w-1/3 bg-blue-100" />
          </div>
        )
      case "single-left-sidelite":
        return (
          <div className={`${baseClasses} flex gap-px`}>
            <div className="w-1/3 bg-blue-100" />
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
          </div>
        )
      case "single-two-sidelites":
        return (
          <div className={`${baseClasses} flex gap-px`}>
            <div className="w-1/4 bg-blue-100" />
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
            <div className="w-1/4 bg-blue-100" />
          </div>
        )
      case "single-transom":
        return (
          <div className={`${baseClasses} flex flex-col gap-px`}>
            <div className="h-1/4 bg-blue-100" />
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-4 bg-gray-800 rounded-full" />
            </div>
          </div>
        )
      case "double-left-sidelite":
        return (
          <div className={`${baseClasses} flex gap-px`}>
            <div className="w-1/4 bg-blue-100" />
            <div className="flex-1 bg-white flex items-center justify-center border-r border-gray-300">
              <div className="w-1 h-4 bg-gray-800 rounded-full" />
            </div>
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-4 bg-gray-800 rounded-full" />
            </div>
          </div>
        )
      case "double-right-sidelite":
        return (
          <div className={`${baseClasses} flex gap-px`}>
            <div className="flex-1 bg-white flex items-center justify-center border-r border-gray-300">
              <div className="w-1 h-4 bg-gray-800 rounded-full" />
            </div>
            <div className="flex-1 bg-white flex items-center justify-center">
              <div className="w-1 h-4 bg-gray-800 rounded-full" />
            </div>
            <div className="w-1/4 bg-blue-100" />
          </div>
        )
      default:
        return <div className={`${baseClasses} bg-gray-100`} />
    }
  }

  return (
    <div className="p-6 bg-gray-50 h-full">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-sm font-semibold text-gray-600 uppercase tracking-wide">CONFIGURATION</h2>
          <ChevronDown className="w-4 h-4 text-orange-500" />
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 overflow-y-auto max-h-[calc(100vh-300px)]">
        {filteredConfigurations.map((config) => (
          <motion.button
            key={config.key}
            onClick={() => setConfiguration(config.key as DoorConfiguration)}
            className={`
              p-4 rounded-lg border-2 text-left transition-all duration-200 bg-white
              ${
                configuration === config.key
                  ? "border-orange-500 shadow-lg"
                  : "border-gray-200 hover:border-gray-300 hover:shadow-md"
              }
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {renderConfigPreview(config.key)}
            <h3 className="font-medium text-gray-900 mt-3 text-xs text-center leading-tight">{config.label}</h3>
            <p className="text-xs text-gray-600 mt-1 text-center">{config.description}</p>
          </motion.button>
        ))}
      </div>
    </div>
  )
}
