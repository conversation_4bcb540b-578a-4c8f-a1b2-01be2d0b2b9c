"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export default function ProductHeroSection() {
  const router = useRouter()

  const handleGetQuote = () => {
    router.push("/door-configurator")
  }

  return (
    <section className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div>
            <h1 className="text-4xl lg:text-5xl font-bebas font-bold text-black mb-6">Premium Patio Doors</h1>
            <p className="text-lg text-gray-600 mb-8 font-poppins">
              Transform your home with our high-quality patio doors. Built for durability, energy efficiency, and style.
              Choose from a variety of configurations to match your home's aesthetic.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={handleGetQuote}
                className="bg-primary-orange hover:bg-orange-600 text-white px-8 py-3 font-poppins font-semibold text-lg"
              >
                GET INSTANT QUOTE
              </Button>
              <Button
                variant="outline"
                className="border-primary-orange text-primary-orange hover:bg-primary-orange hover:text-white px-8 py-3 font-poppins font-semibold text-lg bg-transparent"
              >
                VIEW GALLERY
              </Button>
            </div>
          </div>

          {/* Right Image */}
          <div className="relative">
            <img
              src="/placeholder.svg?height=500&width=600"
              alt="Premium Patio Door"
              className="w-full h-auto rounded-lg shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
