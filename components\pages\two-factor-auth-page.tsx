"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Shield, Smartphone, Key, CheckCircle, AlertTriangle, Copy } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function TwoFactorAuthPage() {
  const [is2FAEnabled, setIs2FAEnabled] = useState(false)
  const [step, setStep] = useState<"setup" | "verify" | "complete">("setup")
  const [verificationCode, setVerificationCode] = useState("")
  const [backupCodes, setBackupCodes] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  // Mock QR code and secret key
  const secretKey = "JBSWY3DPEHPK3PXP"
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/RAF%20Windows:<EMAIL>?secret=${secretKey}&issuer=RAF%20Windows`

  const handleEnable2FA = async () => {
    setIsLoading(true)
    setError("")

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (verificationCode === "123456") {
        // Mock verification
        const codes = ["A1B2C3D4", "E5F6G7H8", "I9J0K1L2", "M3N4O5P6", "Q7R8S9T0", "U1V2W3X4", "Y5Z6A7B8", "C9D0E1F2"]
        setBackupCodes(codes)
        setIs2FAEnabled(true)
        setStep("complete")
      } else {
        setError("Invalid verification code. Please try again.")
      }
    } catch (err) {
      setError("Failed to enable 2FA. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDisable2FA = async () => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))
      setIs2FAEnabled(false)
      setStep("setup")
      setBackupCodes([])
      setVerificationCode("")
    } catch (err) {
      setError("Failed to disable 2FA. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const copyAllCodes = () => {
    const allCodes = backupCodes.join("\n")
    navigator.clipboard.writeText(allCodes)
  }

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
            <Link href="/profile" className="hover:text-primary-orange">
              Profile
            </Link>
            <span>/</span>
            <span>Two-Factor Authentication</span>
          </div>
          <h1 className="font-bebas text-3xl text-gray-900">Two-Factor Authentication</h1>
          <p className="text-gray-600">Add an extra layer of security to your account</p>
        </div>

        {/* Status Card */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    is2FAEnabled ? "bg-green-100" : "bg-gray-100"
                  }`}
                >
                  <Shield className={`w-6 h-6 ${is2FAEnabled ? "text-green-600" : "text-gray-600"}`} />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Two-Factor Authentication</h3>
                  <p className="text-gray-600">
                    {is2FAEnabled
                      ? "Your account is protected with 2FA"
                      : "Add an extra layer of security to your account"}
                  </p>
                </div>
              </div>
              <Badge className={is2FAEnabled ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                {is2FAEnabled ? "Enabled" : "Disabled"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertTriangle className="w-4 h-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {!is2FAEnabled ? (
          <>
            {step === "setup" && (
              <Card>
                <CardHeader>
                  <CardTitle>Set Up Two-Factor Authentication</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="text-center">
                    <h3 className="font-semibold mb-4">Step 1: Install an Authenticator App</h3>
                    <p className="text-gray-600 mb-6">
                      Download and install an authenticator app on your mobile device:
                    </p>
                    <div className="flex justify-center gap-4 mb-6">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                          <Smartphone className="w-8 h-8 text-gray-600" />
                        </div>
                        <p className="text-sm font-medium">Google Authenticator</p>
                      </div>
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                          <Smartphone className="w-8 h-8 text-gray-600" />
                        </div>
                        <p className="text-sm font-medium">Authy</p>
                      </div>
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                          <Smartphone className="w-8 h-8 text-gray-600" />
                        </div>
                        <p className="text-sm font-medium">Microsoft Authenticator</p>
                      </div>
                    </div>
                  </div>

                  <div className="text-center">
                    <h3 className="font-semibold mb-4">Step 2: Scan QR Code</h3>
                    <p className="text-gray-600 mb-6">Open your authenticator app and scan this QR code:</p>
                    <div className="flex justify-center mb-6">
                      <div className="p-4 bg-white border-2 border-gray-200 rounded-lg">
                        <Image
                          src={qrCodeUrl || "/placeholder.svg"}
                          alt="2FA QR Code"
                          width={200}
                          height={200}
                          className="mx-auto"
                        />
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">Can't scan the code? Enter this key manually:</p>
                    <div className="flex items-center justify-center gap-2 mb-6">
                      <code className="bg-gray-100 px-3 py-2 rounded font-mono text-sm">{secretKey}</code>
                      <Button variant="outline" size="sm" onClick={() => copyToClipboard(secretKey)}>
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-4">Step 3: Enter Verification Code</h3>
                    <p className="text-gray-600 mb-4">Enter the 6-digit code from your authenticator app:</p>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="verificationCode">Verification Code</Label>
                        <Input
                          id="verificationCode"
                          value={verificationCode}
                          onChange={(e) => setVerificationCode(e.target.value)}
                          placeholder="123456"
                          maxLength={6}
                          className="text-center text-lg font-mono"
                        />
                      </div>
                      <Button
                        onClick={handleEnable2FA}
                        disabled={verificationCode.length !== 6 || isLoading}
                        className="w-full bg-primary-orange hover:bg-orange-600"
                      >
                        {isLoading ? "Verifying..." : "Enable 2FA"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {step === "complete" && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    Two-Factor Authentication Enabled
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      Your account is now protected with two-factor authentication!
                    </AlertDescription>
                  </Alert>

                  <div>
                    <h3 className="font-semibold mb-4">Backup Codes</h3>
                    <p className="text-gray-600 mb-4">
                      Save these backup codes in a safe place. You can use them to access your account if you lose your
                      phone:
                    </p>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-4">
                        <span className="font-medium">Your Backup Codes</span>
                        <Button variant="outline" size="sm" onClick={copyAllCodes}>
                          <Copy className="w-4 h-4 mr-2" />
                          Copy All
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        {backupCodes.map((code, index) => (
                          <div key={index} className="flex items-center justify-between bg-white p-2 rounded border">
                            <code className="font-mono text-sm">{code}</code>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(code)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                    <Alert className="mt-4 border-yellow-200 bg-yellow-50">
                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                      <AlertDescription className="text-yellow-800">
                        Each backup code can only be used once. Store them securely and don't share them with anyone.
                      </AlertDescription>
                    </Alert>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Manage Two-Factor Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Two-factor authentication is currently enabled for your account.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <Key className="w-8 h-8 text-gray-600" />
                  <div className="flex-1">
                    <h3 className="font-semibold">Authenticator App</h3>
                    <p className="text-sm text-gray-600">You're using an authenticator app for 2FA</p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">Active</Badge>
                </div>

                <div className="flex items-center gap-4 p-4 border rounded-lg">
                  <Key className="w-8 h-8 text-gray-600" />
                  <div className="flex-1">
                    <h3 className="font-semibold">Backup Codes</h3>
                    <p className="text-sm text-gray-600">{backupCodes.length} backup codes available</p>
                  </div>
                  <Button variant="outline" size="sm">
                    View Codes
                  </Button>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-semibold mb-4 text-red-600">Danger Zone</h3>
                <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                  <div>
                    <h4 className="font-medium text-red-800">Disable Two-Factor Authentication</h4>
                    <p className="text-sm text-red-600">This will remove the extra security layer from your account</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={handleDisable2FA}
                    disabled={isLoading}
                    className="border-red-300 text-red-600 hover:bg-red-100 bg-transparent"
                  >
                    {isLoading ? "Disabling..." : "Disable 2FA"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </main>
  )
}
