"use client"

import { notFound } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Download, Share, Calendar, DollarSign, FileText } from "lucide-react"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"

interface QuoteItem {
  id: string
  name: string
  category: string
  quantity: number
  unitPrice: number
  total: number
  specifications: string[]
}

interface Quote {
  id: string
  title: string
  date: string
  validUntil: string
  status: "draft" | "sent" | "viewed" | "accepted" | "expired" | "declined"
  total: number
  items: QuoteItem[]
  notes: string
  customerInfo: {
    name: string
    email: string
    phone: string
    address: string
  }
}

// This would normally come from a database
const getQuote = (id: string): Quote | null => {
  const quotes: Quote[] = [
    {
      id: "Q-2024-001",
      title: "Residential Window Replacement - Main Floor",
      date: "2024-01-20",
      validUntil: "2024-02-20",
      status: "sent",
      total: 2450.0,
      notes: "Customer requested energy-efficient options with white frames",
      customerInfo: {
        name: "<PERSON>",
        email: "<EMAIL>",
        phone: "(*************",
        address: "123 Main St, Toronto, ON M1A 1A1",
      },
      items: [
        {
          id: "1",
          name: "Double Hung Window",
          category: "Windows",
          quantity: 4,
          unitPrice: 299.0,
          total: 1196.0,
          specifications: ["White Frame", "Low-E Glass", "Standard Screen"],
        },
        {
          id: "2",
          name: "Casement Window",
          category: "Windows",
          quantity: 2,
          unitPrice: 329.0,
          total: 658.0,
          specifications: ["White Frame", "Triple Pane", "FlexScreen"],
        },
        {
          id: "3",
          name: "Installation Service",
          category: "Services",
          quantity: 1,
          unitPrice: 596.0,
          total: 596.0,
          specifications: ["Professional Installation", "Cleanup Included"],
        },
      ],
    },
  ]

  return quotes.find((q) => q.id === id) || null
}

export default function QuoteDetailsPage({ params }: { params: { id: string } }) {
  const quote = getQuote(params.id)

  if (!quote) {
    notFound()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "accepted":
        return "bg-green-100 text-green-800"
      case "sent":
        return "bg-blue-100 text-blue-800"
      case "viewed":
        return "bg-yellow-100 text-yellow-800"
      case "draft":
        return "bg-gray-100 text-gray-800"
      case "expired":
      case "declined":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const subtotal = quote.items.reduce((sum, item) => sum + item.total, 0)
  const tax = subtotal * 0.13 // 13% HST
  const total = subtotal + tax

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/profile/quotes">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Quotes
              </Button>
            </Link>
          </div>

          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="font-bebas text-4xl text-gray-900 mb-2">{quote.title}</h1>
              <div className="flex items-center gap-4 text-gray-600">
                <span className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {new Date(quote.date).toLocaleDateString()}
                </span>
                <span>Quote ID: {quote.id}</span>
                <Badge className={getStatusColor(quote.status)}>
                  {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                </Badge>
              </div>
            </div>

            <div className="flex items-center gap-3 mt-4 md:mt-0">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                onClick={() => {
                  const element = document.createElement("a")
                  element.setAttribute(
                    "href",
                    "data:text/plain;charset=utf-8," +
                      encodeURIComponent(`Quote ${quote.id}\n${quote.title}\nTotal: $${quote.total.toFixed(2)}`),
                  )
                  element.setAttribute("download", `quote-${quote.id}.pdf`)
                  element.style.display = "none"
                  document.body.appendChild(element)
                  element.click()
                  document.body.removeChild(element)
                }}
              >
                <Download className="w-4 h-4" />
                Download PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: quote.title,
                      text: `Quote ${quote.id}: ${quote.title}`,
                      url: window.location.href,
                    })
                  } else {
                    navigator.clipboard.writeText(window.location.href)
                    alert("Quote link copied to clipboard!")
                  }
                }}
              >
                <Share className="w-4 h-4" />
                Share
              </Button>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Name</label>
                    <p className="font-poppins">{quote.customerInfo.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Email</label>
                    <p className="font-poppins">{quote.customerInfo.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Phone</label>
                    <p className="font-poppins">{quote.customerInfo.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600">Address</label>
                    <p className="font-poppins">{quote.customerInfo.address}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quote Items */}
            <Card>
              <CardHeader>
                <CardTitle>Quote Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quote.items.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="font-semibold text-lg">{item.name}</h4>
                          <p className="text-sm text-gray-600">{item.category}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-lg">${item.total.toFixed(2)}</p>
                          <p className="text-sm text-gray-600">
                            {item.quantity} × ${item.unitPrice.toFixed(2)}
                          </p>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {item.specifications.map((spec, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Notes */}
            {quote.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-poppins text-gray-700">{quote.notes}</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quote Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Quote Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>HST (13%):</span>
                    <span>${tax.toFixed(2)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total:</span>
                    <span className="text-primary-orange">${total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <div className="text-sm text-gray-600">
                    <strong>Valid Until:</strong> {new Date(quote.validUntil).toLocaleDateString()}
                  </div>

                  {quote.status === "sent" || quote.status === "viewed" ? (
                    <Button className="w-full bg-primary-orange hover:bg-orange-600 text-white">Accept Quote</Button>
                  ) : quote.status === "draft" ? (
                    <Button className="w-full bg-primary-orange hover:bg-orange-600 text-white">Send Quote</Button>
                  ) : null}
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <p>Have questions about this quote?</p>
                  <div className="space-y-2">
                    <p>
                      <strong>Phone:</strong> (*************
                    </p>
                    <p>
                      <strong>Email:</strong> <EMAIL>
                    </p>
                  </div>
                  <Link href="/contact">
                    <Button variant="outline" className="w-full bg-transparent">
                      Contact Us
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
