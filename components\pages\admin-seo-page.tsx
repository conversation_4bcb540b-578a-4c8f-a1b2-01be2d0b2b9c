"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Save, Plus, Edit } from "lucide-react"

interface SEOPage {
  id: string
  path: string
  title: string
  description: string
  keywords: string
  status: "optimized" | "needs-work" | "not-set"
  lastUpdated: string
}

const seoPages: SEOPage[] = [
  {
    id: "1",
    path: "/",
    title: "Premium Windows & Doors | RAF Windows - Quality You Can Trust",
    description:
      "Discover premium vinyl and wood windows, patio doors, and glass units. Energy-efficient, durable, and professionally installed. Get your free quote today!",
    keywords: "windows, doors, vinyl windows, wood windows, patio doors, energy efficient",
    status: "optimized",
    lastUpdated: "2024-01-30",
  },
  {
    id: "2",
    path: "/about",
    title: "About RAF Windows - Your Trusted Window & Door Experts",
    description:
      "Learn about RAF Windows' commitment to quality, innovation, and customer satisfaction. Over 100 years of combined experience in windows and doors.",
    keywords: "about RAF windows, window company, door company, experience, quality",
    status: "optimized",
    lastUpdated: "2024-01-29",
  },
  {
    id: "3",
    path: "/products/vinyl-windows",
    title: "Vinyl Windows - Energy Efficient & Low Maintenance | RAF",
    description:
      "Explore our premium vinyl windows collection. Energy-efficient, low-maintenance, and available in various styles. Perfect for modern homes.",
    keywords: "vinyl windows, energy efficient windows, low maintenance windows",
    status: "needs-work",
    lastUpdated: "2024-01-25",
  },
  {
    id: "4",
    path: "/contact",
    title: "Contact RAF Windows - Get Your Free Quote Today",
    description:
      "Contact RAF Windows for expert consultation and free quotes. Professional installation, quality products, and exceptional customer service.",
    keywords: "contact, free quote, window installation, consultation",
    status: "optimized",
    lastUpdated: "2024-01-28",
  },
  {
    id: "5",
    path: "/blog",
    title: "Window & Door Blog - Tips, Trends & Expert Advice | RAF",
    description:
      "Stay updated with the latest window and door trends, maintenance tips, and expert advice from RAF Windows professionals.",
    keywords: "window blog, door blog, home improvement, window tips",
    status: "not-set",
    lastUpdated: "2024-01-20",
  },
]

const globalSEOSettings = {
  siteName: "RAF Windows",
  defaultTitle: "RAF Windows - Premium Windows & Doors",
  defaultDescription:
    "Premium windows and doors with professional installation. Energy-efficient, durable, and backed by warranty.",
  defaultKeywords:
    "windows, doors, vinyl windows, wood windows, patio doors, energy efficient, professional installation",
  ogImage: "/images/og-default.jpg",
  twitterHandle: "@rafwindows",
  canonicalUrl: "https://panes.com",
  robotsTxt: "User-agent: *\nAllow: /",
  sitemapUrl: "https://panes.com/sitemap.xml",
}

export default function AdminSEOPage() {
  const [selectedPage, setSelectedPage] = useState<SEOPage | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [isEditing, setIsEditing] = useState(false)

  const filteredPages = seoPages.filter(
    (page) =>
      page.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.title.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case "optimized":
        return "default"
      case "needs-work":
        return "secondary"
      case "not-set":
        return "destructive"
      default:
        return "outline"
    }
  }

  const handleSavePage = () => {
    // Save page SEO settings
    setIsEditing(false)
    setSelectedPage(null)
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="font-bebas text-3xl text-black">SEO Management</h1>
          <p className="font-poppins text-gray-600">Manage website SEO settings and optimize search visibility</p>
        </div>
        <Button className="bg-primary-orange hover:bg-orange-600">
          <Plus className="mr-2 h-4 w-4" />
          Add New Page
        </Button>
      </div>

      <Tabs defaultValue="pages" className="space-y-6">
        <TabsList>
          <TabsTrigger value="pages">Page SEO</TabsTrigger>
          <TabsTrigger value="global">Global Settings</TabsTrigger>
          <TabsTrigger value="analytics">SEO Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="pages" className="space-y-6">
          {/* Search */}
          <Card>
            <CardContent className="pt-6">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search pages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* Pages Table */}
          <Card>
            <CardHeader>
              <CardTitle>Page SEO Settings ({filteredPages.length})</CardTitle>
              <CardDescription>Manage meta titles, descriptions, and keywords for each page</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Page Path</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPages.map((page) => (
                    <TableRow key={page.id}>
                      <TableCell className="font-mono text-sm">{page.path}</TableCell>
                      <TableCell className="max-w-xs truncate">{page.title}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(page.status)}>{page.status.replace("-", " ")}</Badge>
                      </TableCell>
                      <TableCell>{page.lastUpdated}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedPage(page)
                            setIsEditing(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Edit Page Modal */}
          {selectedPage && isEditing && (
            <Card>
              <CardHeader>
                <CardTitle>Edit SEO Settings - {selectedPage.path}</CardTitle>
                <CardDescription>Update meta information for better search visibility</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Meta Title</Label>
                  <Input id="title" defaultValue={selectedPage.title} placeholder="Enter page title..." />
                  <p className="text-xs text-gray-500">Recommended: 50-60 characters</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Meta Description</Label>
                  <Textarea
                    id="description"
                    defaultValue={selectedPage.description}
                    placeholder="Enter page description..."
                    rows={3}
                  />
                  <p className="text-xs text-gray-500">Recommended: 150-160 characters</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="keywords">Keywords</Label>
                  <Input
                    id="keywords"
                    defaultValue={selectedPage.keywords}
                    placeholder="Enter keywords separated by commas..."
                  />
                  <p className="text-xs text-gray-500">Separate keywords with commas</p>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleSavePage} className="bg-primary-orange hover:bg-orange-600">
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="global" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Global SEO Settings</CardTitle>
              <CardDescription>Configure site-wide SEO defaults and settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input id="siteName" defaultValue={globalSEOSettings.siteName} placeholder="Enter site name..." />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="canonicalUrl">Canonical URL</Label>
                  <Input
                    id="canonicalUrl"
                    defaultValue={globalSEOSettings.canonicalUrl}
                    placeholder="https://example.com"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultTitle">Default Title Template</Label>
                <Input
                  id="defaultTitle"
                  defaultValue={globalSEOSettings.defaultTitle}
                  placeholder="Enter default title template..."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultDescription">Default Description</Label>
                <Textarea
                  id="defaultDescription"
                  defaultValue={globalSEOSettings.defaultDescription}
                  placeholder="Enter default description..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultKeywords">Default Keywords</Label>
                <Input
                  id="defaultKeywords"
                  defaultValue={globalSEOSettings.defaultKeywords}
                  placeholder="Enter default keywords..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="ogImage">Default OG Image</Label>
                  <Input id="ogImage" defaultValue={globalSEOSettings.ogImage} placeholder="/images/og-default.jpg" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="twitterHandle">Twitter Handle</Label>
                  <Input id="twitterHandle" defaultValue={globalSEOSettings.twitterHandle} placeholder="@username" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="robotsTxt">Robots.txt Content</Label>
                <Textarea
                  id="robotsTxt"
                  defaultValue={globalSEOSettings.robotsTxt}
                  placeholder="User-agent: *..."
                  rows={4}
                />
              </div>

              <Button className="bg-primary-orange hover:bg-orange-600">
                <Save className="mr-2 h-4 w-4" />
                Save Global Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Search Visibility</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">87%</div>
                <p className="text-sm text-gray-600">Pages indexed</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Avg. Position</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">12.4</div>
                <p className="text-sm text-gray-600">Search ranking</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Click-through Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-600">3.2%</div>
                <p className="text-sm text-gray-600">From search results</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>SEO Performance Summary</CardTitle>
              <CardDescription>Overview of your website's search performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Pages Optimized</p>
                    <p className="text-sm text-gray-600">
                      {seoPages.filter((p) => p.status === "optimized").length} of {seoPages.length} pages
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg text-green-600">
                      {Math.round((seoPages.filter((p) => p.status === "optimized").length / seoPages.length) * 100)}%
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Pages Need Work</p>
                    <p className="text-sm text-gray-600">
                      {seoPages.filter((p) => p.status === "needs-work").length} pages require attention
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg text-yellow-600">
                      {seoPages.filter((p) => p.status === "needs-work").length}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Pages Not Set</p>
                    <p className="text-sm text-gray-600">
                      {seoPages.filter((p) => p.status === "not-set").length} pages missing SEO data
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg text-red-600">
                      {seoPages.filter((p) => p.status === "not-set").length}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
