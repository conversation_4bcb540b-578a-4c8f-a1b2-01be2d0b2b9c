@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    /* Apply <PERSON><PERSON>s as default font for body */
    font-family: var(--font-poppins), system-ui, sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    /* Apply <PERSON>bas Neue for headings */
    font-family: var(--font-bebas-neue), cursive;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

@layer components {
  .bebas {
    font-family: var(--font-bebas-neue), cursive;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .poppins {
    font-family: var(--font-poppins), system-ui, sans-serif;
  }
}

/* Custom animations for overlays */
@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}
