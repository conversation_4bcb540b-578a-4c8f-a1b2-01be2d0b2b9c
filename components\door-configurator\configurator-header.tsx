"use client"

import Link from "next/link"

export function ConfiguratorHeader() {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Left - Logo */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <span className="font-bold text-lg">FACTORY WINDOWS</span>
          </div>
          <span className="text-gray-600 font-medium">Entrance builder</span>
        </div>

        {/* Right - Navigation */}
        <div className="flex items-center space-x-6">
          <Link href="/sign-in" className="text-gray-700 hover:text-orange-500 font-medium">
            Login
          </Link>
          <Link href="#" className="text-gray-700 hover:text-orange-500 font-medium">
            Eng
          </Link>
        </div>
      </div>
    </header>
  )
}
