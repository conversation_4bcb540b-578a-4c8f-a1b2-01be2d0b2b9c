"use client"

import { motion, AnimatePresence } from "framer-motion"
import { useDoorConfiguratorStore } from "@/lib/door-configurator-store"
import { ConfigurationStep } from "./steps/configuration-step"
import { DoorStep } from "./steps/door-step"
import { DoorglassStep } from "./steps/doorglass-step"
import { ColorsStep } from "./steps/colors-step"
import { HandleStep } from "./steps/handle-step"
import { SummaryStep } from "./steps/summary-step"

export function StepContent() {
  const { currentStep } = useDoorConfiguratorStore()

  const renderStep = () => {
    switch (currentStep) {
      case "configuration":
        return <ConfigurationStep />
      case "door":
        return <DoorStep />
      case "doorglass":
        return <DoorglassStep />
      case "colors":
        return <ColorsStep />
      case "handle":
        return <HandleStep />
      case "summary":
        return <SummaryStep />
      default:
        return <ConfigurationStep />
    }
  }

  return (
    <div className="w-96 bg-gray-50 border-r border-gray-200 overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 20 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="h-full"
        >
          {renderStep()}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
