"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore, type DoorColor } from "@/lib/door-configurator-store"
import { Search } from "lucide-react"

const colorOptions: { key: DoorColor; label: string; color: string }[] = [
  { key: "white", label: "White", color: "bg-white" },
  { key: "black", label: "Black", color: "bg-gray-900" },
  { key: "brown", label: "Brown", color: "bg-amber-800" },
  { key: "beige", label: "Beige", color: "bg-amber-100" },
  { key: "gray", label: "Gray", color: "bg-gray-500" },
]

export function ColorsStep() {
  const { doorColor, setDoorColor } = useDoorConfiguratorStore()

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">DOOR COLORS</h2>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {colorOptions.map((color) => (
          <motion.button
            key={color.key}
            onClick={() => setDoorColor(color.key)}
            className={`
              p-4 rounded-lg border-2 text-left transition-all duration-200
              ${
                doorColor === color.key
                  ? "border-orange-500 bg-orange-50"
                  : "border-gray-200 bg-white hover:border-gray-300"
              }
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className={`w-full h-24 ${color.color} rounded border border-gray-300 mb-3`} />
            <h3 className="font-medium text-gray-900">{color.label}</h3>
          </motion.button>
        ))}
      </div>
    </div>
  )
}
