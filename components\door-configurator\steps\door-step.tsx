"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore, type DoorStyle } from "@/lib/door-configurator-store"
import { Search, ChevronDown } from "lucide-react"
import { useState } from "react"

const doorStyles: { key: DoorStyle; label: string }[] = [
  { key: "without-door-lite", label: "Without Door Lite" },
  { key: "1/2-lite", label: "1/2 Lite" },
  { key: "3/4-lite", label: "3/4 Lite" },
  { key: "1/4-lite", label: "1/4 Lite" },
  { key: "vertical-door-lite", label: "Vertical Door Lite" },
  { key: "full-lite", label: "Full Lite" },
  { key: "3-lites", label: "3 Lites" },
  { key: "4-lites", label: "4 Lites" },
  { key: "centered-vertical-door-lite", label: "Centered Vertical Door Lite" },
]

export function DoorStep() {
  const { doorStyle, setDoorStyle } = useDoorConfiguratorStore()
  const [searchQuery, setSearchQuery] = useState("")

  const filteredDoorStyles = doorStyles.filter((style) => style.label.toLowerCase().includes(searchQuery.toLowerCase()))

  const renderDoorPreview = (style: DoorStyle) => {
    const baseClasses = "w-full h-28 bg-white border-2 border-gray-300 rounded relative overflow-hidden"

    switch (style) {
      case "without-door-lite":
        return (
          <div className={`${baseClasses} bg-orange-500`}>
            <div className="w-full h-full bg-white m-1 rounded-sm flex items-center justify-center">
              <div className="w-1 h-8 bg-gray-800 rounded-full" />
            </div>
          </div>
        )

      case "1/2-lite":
        return (
          <div className={baseClasses}>
            <div className="h-1/2 bg-blue-100 border-b border-gray-300" />
            <div className="h-1/2 bg-white flex items-center justify-center">
              <div className="w-1 h-4 bg-gray-800 rounded-full" />
            </div>
          </div>
        )

      case "3/4-lite":
        return (
          <div className={baseClasses}>
            <div className="h-3/4 bg-blue-100 border-b border-gray-300" />
            <div className="h-1/4 bg-white flex items-center justify-center">
              <div className="w-1 h-2 bg-gray-800 rounded-full" />
            </div>
          </div>
        )

      case "1/4-lite":
        return (
          <div className={baseClasses}>
            <div className="h-1/4 bg-blue-100 border-b border-gray-300" />
            <div className="h-3/4 bg-white flex items-center justify-center">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
          </div>
        )

      case "full-lite":
        return <div className={`${baseClasses} bg-blue-100`} />

      case "vertical-door-lite":
        return (
          <div className={`${baseClasses} flex`}>
            <div className="w-1/3 bg-blue-100 border-r border-gray-300" />
            <div className="w-2/3 bg-white flex items-center justify-center">
              <div className="w-1 h-6 bg-gray-800 rounded-full" />
            </div>
          </div>
        )

      case "3-lites":
        return (
          <div className={`${baseClasses} flex flex-col gap-px`}>
            <div className="flex-1 bg-blue-100" />
            <div className="flex-1 bg-blue-100" />
            <div className="flex-1 bg-blue-100" />
          </div>
        )

      case "4-lites":
        return (
          <div className={`${baseClasses} grid grid-cols-2 grid-rows-2 gap-px`}>
            <div className="bg-blue-100" />
            <div className="bg-blue-100" />
            <div className="bg-blue-100" />
            <div className="bg-blue-100" />
          </div>
        )

      case "centered-vertical-door-lite":
        return (
          <div className={`${baseClasses} flex justify-center items-center px-4`}>
            <div className="w-1/2 h-3/4 bg-blue-100 border border-gray-300 rounded" />
          </div>
        )

      default:
        return <div className={`${baseClasses} bg-gray-100`} />
    }
  }

  return (
    <div className="p-6 bg-gray-50 h-full">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-sm font-semibold text-gray-600 uppercase tracking-wide">DOOR STYLE</h2>
          <ChevronDown className="w-4 h-4 text-orange-500" />
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 overflow-y-auto max-h-[calc(100vh-300px)]">
        {filteredDoorStyles.map((style) => (
          <motion.button
            key={style.key}
            onClick={() => setDoorStyle(style.key)}
            className={`
              p-4 rounded-lg border-2 text-left transition-all duration-200 bg-white
              ${
                doorStyle === style.key
                  ? "border-orange-500 shadow-lg"
                  : "border-gray-200 hover:border-gray-300 hover:shadow-md"
              }
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {renderDoorPreview(style.key)}
            <h3 className="font-medium text-gray-900 mt-3 text-xs text-center leading-tight">{style.label}</h3>
          </motion.button>
        ))}
      </div>
    </div>
  )
}
