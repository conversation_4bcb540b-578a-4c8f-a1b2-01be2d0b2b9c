"use client"
import { v4 as uuidv4 } from "uuid"

export interface User {
  id: string
  name: string
  email: string
  role: "user" | "admin"
}

export interface PaymentMethod {
  id: string
  userId: string
  type: "credit" | "debit" | "paypal"
  cardNumber: string
  expiryDate: string
  cardholderName: string
  isDefault: boolean
  createdAt: string
}

export interface ActivityLog {
  id: string
  userId: string
  userName: string
  action: string
  description: string
  timestamp: string
  ipAddress: string
}

// Demo users for authentication
export const DEMO_USERS = {
  admin: {
    id: "admin-123",
    name: "Admin User",
    email: "<EMAIL>",
    password: "adminpassword", // In a real app, never store passwords like this
    role: "admin" as const,
  },
  user: {
    id: "user-456",
    name: "Demo User",
    email: "<EMAIL>",
    password: "userpassword", // In a real app, never store passwords like this
    role: "user" as const,
  },
}

// Simulate user authentication
export function login(email: string, password: string): User | null {
  const adminUser = DEMO_USERS.admin
  const regularUser = DEMO_USERS.user

  if (email === adminUser.email && password === adminUser.password) {
    const userToStore = { id: adminUser.id, name: adminUser.name, email: adminUser.email, role: adminUser.role }
    localStorage.setItem("currentUser", JSON.stringify(userToStore))
    addActivityLog(adminUser.id, adminUser.name, "login", "Admin user logged in")
    return userToStore
  } else if (email === regularUser.email && password === regularUser.password) {
    const userToStore = { id: regularUser.id, name: regularUser.name, email: regularUser.email, role: regularUser.role }
    localStorage.setItem("currentUser", JSON.stringify(userToStore))
    addActivityLog(regularUser.id, regularUser.name, "login", "Regular user logged in")
    return userToStore
  }
  return null
}

export function logout(): void {
  const user = getCurrentUser()
  if (user) {
    addActivityLog(user.id, user.name, "logout", "User logged out")
  }
  localStorage.removeItem("currentUser")
  // Use window.location.replace instead of href to prevent back button issues
  if (typeof window !== "undefined") {
    window.location.replace("/")
  }
}

export function isAuthenticated(): boolean {
  if (typeof window === "undefined") return false
  return !!localStorage.getItem("currentUser")
}

export function getCurrentUser(): User | null {
  if (typeof window === "undefined") return null
  const user = localStorage.getItem("currentUser")
  return user ? JSON.parse(user) : null
}

export function isAdmin(): boolean {
  const user = getCurrentUser()
  return user?.role === "admin"
}

// Simulate activity logging
export function addActivityLog(userId: string, userName: string, action: string, description: string): void {
  if (typeof window === "undefined") return
  const logs = JSON.parse(localStorage.getItem("activityLogs") || "[]") as ActivityLog[]
  const newLog: ActivityLog = {
    id: uuidv4(),
    userId,
    userName,
    action,
    description,
    timestamp: new Date().toISOString(),
    ipAddress: "***********", // Placeholder for demo
  }
  logs.unshift(newLog) // Add to the beginning for most recent first
  localStorage.setItem("activityLogs", JSON.stringify(logs))
}

export function getActivityLogs(): ActivityLog[] {
  if (typeof window === "undefined") return []
  return JSON.parse(localStorage.getItem("activityLogs") || "[]")
}

// Simulate user registration (for demo purposes)
export function registerUser(name: string, email: string, password: string): User | null {
  // In a real application, you would hash the password and store it securely in a database.
  // This is a simplified demo.
  const newUser: User = {
    id: uuidv4(),
    name,
    email,
    role: "user",
  }
  // For demo, we'll just "register" them by logging them in
  localStorage.setItem("currentUser", JSON.stringify(newUser))
  addActivityLog(newUser.id, newUser.name, "register", "New user registered")
  return newUser
}

// Simulate account deletion
export function deleteAccount(userId: string): boolean {
  const currentUser = getCurrentUser()
  if (currentUser && currentUser.id === userId) {
    localStorage.removeItem("currentUser")
    addActivityLog(userId, currentUser.name, "account_deleted", "User account deleted")
    return true
  }
  return false
}

// Simulate password change
export function changePassword(userId: string, newPassword: string): boolean {
  const currentUser = getCurrentUser()
  if (currentUser && currentUser.id === userId) {
    // In a real app, you'd update the hashed password in the database
    addActivityLog(userId, currentUser.name, "password_change", "User changed password")
    return true
  }
  return false
}

// Simulate 2FA toggle
export function toggleTwoFactor(userId: string, enable: boolean): boolean {
  const currentUser = getCurrentUser()
  if (currentUser && currentUser.id === userId) {
    // In a real app, you'd update 2FA status in the database
    addActivityLog(
      userId,
      currentUser.name,
      enable ? "2fa_enabled" : "2fa_disabled",
      `2FA ${enable ? "enabled" : "disabled"}`,
    )
    return true
  }
  return false
}

// Demo payment methods
const paymentMethods: PaymentMethod[] = [
  {
    id: "pm1",
    userId: "user-456",
    type: "credit",
    cardNumber: "**** **** **** 1234",
    expiryDate: "12/26",
    cardholderName: "Demo User",
    isDefault: true,
    createdAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "pm2",
    userId: "user-456",
    type: "credit",
    cardNumber: "**** **** **** 5678",
    expiryDate: "08/25",
    cardholderName: "Demo User",
    isDefault: false,
    createdAt: "2024-01-20T14:30:00Z",
  },
]

// Payment Methods
export const getPaymentMethods = (userId: string): PaymentMethod[] => {
  return paymentMethods.filter((pm) => pm.userId === userId)
}

export const addPaymentMethod = (paymentMethod: Omit<PaymentMethod, "id" | "createdAt">): PaymentMethod => {
  const newPaymentMethod: PaymentMethod = {
    ...paymentMethod,
    id: "pm_" + Date.now(),
    createdAt: new Date().toISOString(),
  }

  paymentMethods.push(newPaymentMethod)
  addActivityLog(paymentMethod.userId, paymentMethod.cardholderName, "payment_method_added", "Added new payment method")

  return newPaymentMethod
}

export const updatePaymentMethod = (id: string, updates: Partial<PaymentMethod>): boolean => {
  const index = paymentMethods.findIndex((pm) => pm.id === id)
  if (index !== -1) {
    paymentMethods[index] = { ...paymentMethods[index], ...updates }
    addActivityLog(
      paymentMethods[index].userId,
      paymentMethods[index].cardholderName,
      "payment_method_updated",
      "Updated payment method",
    )
    return true
  }
  return false
}

export const deletePaymentMethod = (id: string): boolean => {
  const index = paymentMethods.findIndex((pm) => pm.id === id)
  if (index !== -1) {
    const userId = paymentMethods[index].userId
    const cardholderName = paymentMethods[index].cardholderName
    paymentMethods.splice(index, 1)
    addActivityLog(userId, cardholderName, "payment_method_deleted", "Deleted payment method")
    return true
  }
  return false
}

// Hook for components
export const useAuth = () => {
  return {
    login,
    logout,
    isAuthenticated,
    getCurrentUser,
    isAdmin,
  }
}
