"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Terminal, TriangleAlert } from "lucide-react"
import { deleteAccount, getCurrentUser } from "@/lib/auth"
import { useToast } from "@/hooks/use-toast"

export default function DeleteAccountPage() {
  const [step, setStep] = useState(1)
  const [password, setPassword] = useState("")
  const [confirmDelete, setConfirmDelete] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  const { toast } = useToast()
  const currentUser = getCurrentUser()

  const handleConfirmPassword = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    // In a real app, you'd verify the password against the backend
    // For this demo, we'll just check against the demo user's password
    if (currentUser?.email === "<EMAIL>" && password === "userpassword") {
      setStep(2)
    } else {
      setError("Incorrect password. Please try again.")
    }
  }

  const handleDeleteAccount = () => {
    setError("")
    if (!confirmDelete) {
      setError("Please confirm you understand the implications of deleting your account.")
      return
    }

    if (currentUser && deleteAccount(currentUser.id)) {
      toast({
        title: "Account Deleted",
        description: "Your account has been successfully deleted.",
        variant: "default",
      })
      router.push("/account-deleted")
    } else {
      setError("Failed to delete account. Please try again.")
    }
  }

  if (!currentUser) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>Please sign in to manage your account.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => router.push("/sign-in")}>Sign In</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-lg border-red-300 bg-red-50">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bebas text-red-700">Delete Your Account</CardTitle>
          <CardDescription className="font-poppins text-red-600">
            This action is irreversible. Please read carefully.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {step === 1 && (
            <form onSubmit={handleConfirmPassword} className="space-y-4">
              <Alert variant="destructive">
                <TriangleAlert className="h-4 w-4" />
                <AlertTitle>Warning!</AlertTitle>
                <AlertDescription>
                  Deleting your account will permanently remove all your data, including orders, quotes, and profile
                  information. This cannot be undone.
                </AlertDescription>
              </Alert>
              <div className="space-y-2">
                <Label htmlFor="password">Enter your password to continue</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              {error && <p className="text-sm text-red-500">{error}</p>}
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button type="submit" variant="destructive">
                  Confirm
                </Button>
              </div>
            </form>
          )}

          {step === 2 && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <Terminal className="h-4 w-4" />
                <AlertTitle>Are you absolutely sure?</AlertTitle>
                <AlertDescription>
                  This will permanently delete your account and all associated data. This action cannot be undone.
                  Please type &quot;DELETE MY ACCOUNT&quot; to confirm.
                </AlertDescription>
              </Alert>
              <div className="space-y-2">
                <Label htmlFor="confirmText">Type &quot;DELETE MY ACCOUNT&quot; to confirm</Label>
                <Input
                  id="confirmText"
                  type="text"
                  placeholder="DELETE MY ACCOUNT"
                  value={confirmDelete ? "DELETE MY ACCOUNT" : ""}
                  onChange={(e) => setConfirmDelete(e.target.value === "DELETE MY ACCOUNT")}
                  className={!confirmDelete ? "border-red-500" : ""}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="understand"
                  checked={confirmDelete}
                  onCheckedChange={(checked) => setConfirmDelete(!!checked)}
                />
                <label
                  htmlFor="understand"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I understand this action is irreversible.
                </label>
              </div>
              {error && <p className="text-sm text-red-500">{error}</p>}
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setStep(1)}>
                  Back
                </Button>
                <Button type="button" variant="destructive" onClick={handleDeleteAccount} disabled={!confirmDelete}>
                  Delete My Account
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
