"use client"

interface QuoteData {
  configuration: string
  doorStyle: string
  doorGlass: string
  doorColor: string
  doorHandle: string
  price: number
  date: string
  quoteNumber: string
}

export async function generateDoorQuotePDF(quoteData: QuoteData) {
  try {
    // In a real implementation, you would use a PDF library like jsPDF or react-pdf
    // For now, we'll create a simple HTML-based PDF generation

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Door Configuration Quote</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 48px; font-weight: bold; color: #f97316; }
            .quote-info { margin-bottom: 30px; }
            .config-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            .config-table th, .config-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            .config-table th { background-color: #f97316; color: white; }
            .total { font-size: 24px; font-weight: bold; text-align: right; margin-top: 20px; }
            .footer { margin-top: 50px; text-align: center; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="logo">RAF</div>
            <h2>Patio Door Configuration Quote</h2>
          </div>
          
          <div class="quote-info">
            <p><strong>Quote Number:</strong> ${quoteData.quoteNumber}</p>
            <p><strong>Date:</strong> ${quoteData.date}</p>
          </div>
          
          <table class="config-table">
            <tr>
              <th>Configuration Option</th>
              <th>Selection</th>
            </tr>
            <tr>
              <td>Door Configuration</td>
              <td>${quoteData.configuration.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</td>
            </tr>
            <tr>
              <td>Door Style</td>
              <td>${quoteData.doorStyle.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</td>
            </tr>
            <tr>
              <td>Glass Type</td>
              <td>${quoteData.doorGlass.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</td>
            </tr>
            <tr>
              <td>Door Color</td>
              <td>${quoteData.doorColor.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</td>
            </tr>
            <tr>
              <td>Handle Style</td>
              <td>${quoteData.doorHandle.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</td>
            </tr>
          </table>
          
          <div class="total">
            Total Price: $${quoteData.price.toFixed(2)}
          </div>
          
          <div class="footer">
            <p>This quote is valid for 30 days from the date of issue.</p>
            <p>Contact us at (123) 456-7890 for more information.</p>
            <p>© 2025 RAF - All Rights Reserved</p>
          </div>
        </body>
      </html>
    `

    // Create a new window with the HTML content
    const printWindow = window.open("", "_blank")
    if (printWindow) {
      printWindow.document.write(htmlContent)
      printWindow.document.close()

      // Wait for content to load, then print
      setTimeout(() => {
        printWindow.print()
        printWindow.close()
      }, 500)
    }
  } catch (error) {
    console.error("Error generating PDF:", error)
    alert("Error generating PDF. Please try again.")
  }
}
