"use client"

import { useState } from "react"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import FixedElements from "@/components/layout/fixed-elements"
import { Input } from "@/components/ui/input"
import { Search, ChevronDown, ChevronUp } from "lucide-react"

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
}

interface FAQCategory {
  name: string
  icon: string
  count: number
}

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const faqData: FAQItem[] = [
    // Product Information
    {
      id: "1",
      question: "What types of windows do you manufacture?",
      answer:
        "We manufacture a wide range of vinyl windows including casement, awning, single and double hung, slider windows, picture windows, and custom shapes. All our windows are energy-efficient and built to last.",
      category: "products",
    },
    {
      id: "2",
      question: "What is the difference between double and triple pane windows?",
      answer:
        "Double pane windows have two layers of glass with an insulating gas between them, while triple pane windows have three layers. Triple pane windows offer superior energy efficiency and noise reduction but come at a higher cost.",
      category: "products",
    },
    {
      id: "3",
      question: "Do you offer custom window sizes and shapes?",
      answer:
        "Yes, we specialize in custom windows. We can manufacture windows in virtually any size or shape to fit your specific needs, including arched, circular, and other specialty shapes.",
      category: "products",
    },
    {
      id: "4",
      question: "What glass options are available?",
      answer:
        "We offer various glass options including Low-E coatings, argon gas fills, tempered glass, laminated glass, and decorative glass options. Our team can help you choose the best option for your needs.",
      category: "products",
    },

    // Installation
    {
      id: "5",
      question: "Do you provide installation services?",
      answer:
        "Yes, we work with certified installation partners in your area. We can arrange professional installation or provide detailed instructions for DIY installation.",
      category: "installation",
    },
    {
      id: "6",
      question: "How long does installation typically take?",
      answer:
        "Installation time varies depending on the number of windows and complexity of the project. Typically, a single window takes 1-2 hours, while a full home installation may take 1-3 days.",
      category: "installation",
    },
    {
      id: "7",
      question: "What should I expect during the installation process?",
      answer:
        "Our installation team will protect your home, remove old windows carefully, install new windows with proper sealing and insulation, clean up the work area, and conduct a final inspection with you.",
      category: "installation",
    },
    {
      id: "8",
      question: "Can windows be installed in winter?",
      answer:
        "Yes, windows can be installed year-round. Our installation teams are experienced in cold weather installations and take extra precautions to minimize heat loss during the process.",
      category: "installation",
    },

    // Pricing & Ordering
    {
      id: "9",
      question: "How do I get a quote for my windows?",
      answer:
        "You can get an instant quote using our online configurator, schedule a free in-home estimate, or contact our sales team directly. We provide detailed quotes with no hidden fees.",
      category: "pricing",
    },
    {
      id: "10",
      question: "What payment methods do you accept?",
      answer:
        "We accept all major credit cards, PayPal, bank transfers, and offer financing options. Payment terms vary depending on the size of your order.",
      category: "pricing",
    },
    {
      id: "11",
      question: "Do you offer financing options?",
      answer:
        "Yes, we partner with several financing companies to offer flexible payment plans. Options include 0% interest for qualified buyers and extended payment terms.",
      category: "pricing",
    },
    {
      id: "12",
      question: "What is included in the window price?",
      answer:
        "Our window prices include the window unit, standard hardware, weatherstripping, and basic installation materials. Additional costs may apply for special hardware or complex installations.",
      category: "pricing",
    },

    // Warranty & Service
    {
      id: "13",
      question: "What warranty do you offer on your windows?",
      answer:
        "We offer a comprehensive warranty covering materials and workmanship. Vinyl frames are covered for life, glass units for 20 years, and hardware for 10 years.",
      category: "warranty",
    },
    {
      id: "14",
      question: "How do I file a warranty claim?",
      answer:
        "To file a warranty claim, contact our customer service team with your order number and photos of the issue. We'll guide you through the process and arrange for repair or replacement as needed.",
      category: "warranty",
    },
    {
      id: "15",
      question: "What maintenance is required for vinyl windows?",
      answer:
        "Vinyl windows require minimal maintenance. Regular cleaning with mild soap and water, lubricating moving parts annually, and checking weatherstripping will keep your windows in excellent condition.",
      category: "warranty",
    },
    {
      id: "16",
      question: "Do you provide repair services for existing windows?",
      answer:
        "Yes, we provide repair services for windows we've manufactured. Common repairs include hardware replacement, glass unit replacement, and weatherstripping updates.",
      category: "warranty",
    },

    // Shipping & Delivery
    {
      id: "17",
      question: "How long does it take to manufacture custom windows?",
      answer:
        "Custom windows typically take 10-15 business days to manufacture, depending on the complexity and current order volume. Rush orders may be available for an additional fee.",
      category: "shipping",
    },
    {
      id: "18",
      question: "What are your shipping options?",
      answer:
        "We offer standard ground shipping, expedited shipping, and white glove delivery service. Local customers can also arrange pickup from our facility.",
      category: "shipping",
    },
    {
      id: "19",
      question: "How are windows packaged for shipping?",
      answer:
        "Windows are carefully packaged in custom crates with protective materials to prevent damage during transit. Each window is individually wrapped and secured.",
      category: "shipping",
    },
    {
      id: "20",
      question: "What if my windows arrive damaged?",
      answer:
        "If windows arrive damaged, contact us immediately with photos. We'll arrange for replacement windows to be shipped at no cost and coordinate return of damaged items.",
      category: "shipping",
    },
  ]

  const categories: FAQCategory[] = [
    { name: "All Categories", icon: "📋", count: faqData.length },
    { name: "Products", icon: "🪟", count: faqData.filter((item) => item.category === "products").length },
    { name: "Installation", icon: "🔧", count: faqData.filter((item) => item.category === "installation").length },
    { name: "Pricing & Ordering", icon: "💰", count: faqData.filter((item) => item.category === "pricing").length },
    { name: "Warranty & Service", icon: "🛡️", count: faqData.filter((item) => item.category === "warranty").length },
    { name: "Shipping & Delivery", icon: "🚚", count: faqData.filter((item) => item.category === "shipping").length },
  ]

  const filteredFAQs = faqData.filter((item) => {
    const matchesSearch =
      item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "all" || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const toggleExpanded = (id: string) => {
    setExpandedItems((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  const getCategoryKey = (categoryName: string) => {
    return categoryName.toLowerCase().replace(/[^a-z]/g, "")
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="py-12 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bebas text-black mb-4">Frequently Asked Questions</h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Find answers to common questions about our windows, installation process, pricing, and more. Can't find
              what you're looking for? Contact our support team.
            </p>
          </div>

          {/* Search Bar */}
          <div className="mb-8">
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search frequently asked questions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 py-3 text-lg"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Categories Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-24">
                <h2 className="text-xl font-bebas text-black mb-4">Categories</h2>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={getCategoryKey(category.name)}
                      onClick={() => setSelectedCategory(getCategoryKey(category.name))}
                      className={`w-full text-left p-3 rounded-lg transition-all ${
                        selectedCategory === getCategoryKey(category.name)
                          ? "bg-primary-orange text-white"
                          : "hover:bg-gray-50"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{category.icon}</span>
                          <span className="font-medium">{category.name}</span>
                        </div>
                        <span
                          className={`text-sm px-2 py-1 rounded-full ${
                            selectedCategory === getCategoryKey(category.name)
                              ? "bg-white bg-opacity-20"
                              : "bg-gray-100"
                          }`}
                        >
                          {category.count}
                        </span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* FAQ Content */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm">
                {filteredFAQs.length > 0 ? (
                  <div className="divide-y divide-gray-200">
                    {filteredFAQs.map((faq) => (
                      <div key={faq.id} className="p-6">
                        <button
                          onClick={() => toggleExpanded(faq.id)}
                          className="w-full text-left flex items-center justify-between group"
                        >
                          <h3 className="text-lg font-semibold text-black group-hover:text-primary-orange transition-colors pr-4">
                            {faq.question}
                          </h3>
                          {expandedItems.includes(faq.id) ? (
                            <ChevronUp className="w-5 h-5 text-gray-400 flex-shrink-0" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                          )}
                        </button>

                        {expandedItems.includes(faq.id) && (
                          <div className="mt-4 text-gray-700 leading-relaxed">{faq.answer}</div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-12 text-center">
                    <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-bebas text-gray-500 mb-2">No Results Found</h3>
                    <p className="text-gray-500">Try adjusting your search terms or browse different categories.</p>
                  </div>
                )}
              </div>

              {/* Contact Support */}
              <div className="mt-8 bg-primary-orange rounded-lg p-6 text-white text-center">
                <h3 className="text-2xl font-bebas mb-2">Still Have Questions?</h3>
                <p className="mb-4">
                  Our customer support team is here to help you with any questions not covered in our FAQ.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/contact"
                    className="bg-white text-primary-orange px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                  >
                    Contact Support
                  </a>
                  <a
                    href="tel:(*************"
                    className="border border-white text-white px-6 py-2 rounded-lg font-medium hover:bg-white hover:bg-opacity-10 transition-colors"
                  >
                    Call (*************
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
      <FixedElements />
    </div>
  )
}
