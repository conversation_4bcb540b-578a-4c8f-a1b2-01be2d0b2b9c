"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore, type ConfiguratorStep } from "@/lib/door-configurator-store"
import { ChevronLeft } from "lucide-react"
import { useRouter } from "next/navigation"

const steps: { key: ConfiguratorStep; label: string; number: number }[] = [
  { key: "configuration", label: "Configuration", number: 1 },
  { key: "door", label: "Door", number: 2 },
  { key: "doorglass", label: "Doorglass", number: 3 },
  { key: "colors", label: "Colors", number: 4 },
  { key: "handle", label: "Handle", number: 5 },
  { key: "summary", label: "Summary", number: 6 },
]

export function ProgressBar() {
  const { currentStep, setCurrentStep, getPreviousStep } = useDoorConfiguratorStore()
  const router = useRouter()

  const currentStepIndex = steps.findIndex((step) => step.key === currentStep)

  const handleBackButtonClick = () => {
    if (currentStepIndex === 0) {
      // If on the first step, go back to patio doors page
      router.push("/products/patio-doors")
    } else {
      // Otherwise, go to the previous step using the store's logic
      const prevStep = getPreviousStep()
      if (prevStep) {
        setCurrentStep(prevStep)
      }
    }
  }

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStepIndex) return "completed"
    if (stepIndex === currentStepIndex) return "current"
    return "upcoming"
  }

  return (
    <div className="bg-gray-800 text-white">
      <div className="flex items-center">
        {/* Back Button */}
        <button
          className="p-4 hover:bg-gray-700 transition-colors"
          onClick={handleBackButtonClick}
          aria-label="Go back"
        >
          <ChevronLeft className="w-5 h-5" />
        </button>

        {/* Steps */}
        <div className="flex flex-1">
          {steps.map((step, index) => {
            const status = getStepStatus(index)
            const isClickable = index <= currentStepIndex

            return (
              <div key={step.key} className="flex-1 relative">
                <motion.button
                  onClick={() => isClickable && setCurrentStep(step.key)}
                  disabled={!isClickable}
                  className={`
                    w-full px-6 py-4 text-left relative transition-all duration-200
                    ${
                      status === "current"
                        ? "bg-orange-500 text-white"
                        : status === "completed"
                          ? "bg-gray-700 text-white hover:bg-gray-600"
                          : "bg-gray-800 text-gray-400"
                    }
                    ${isClickable ? "cursor-pointer" : "cursor-not-allowed"}
                  `}
                  style={{
                    clipPath:
                      index === steps.length - 1
                        ? "none"
                        : "polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%)",
                  }}
                  whileHover={isClickable ? { scale: 1.02 } : {}}
                >
                  <div className="flex items-center">
                    <span
                      className={`
                        w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs font-bold mr-3
                        ${
                          status === "current" || status === "completed"
                            ? "bg-white text-orange-500 border-white"
                            : "border-gray-400 text-gray-400"
                        }
                      `}
                    >
                      {step.number}
                    </span>
                    <span className="font-medium">{step.label}</span>
                  </div>
                </motion.button>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
