import { Suspense } from "react"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import PaymentMethodsPage from "@/components/pages/payment-methods-page"

export default function PaymentMethodsPageRoute() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <Suspense fallback={<div>Loading...</div>}>
            <PaymentMethodsPage />
          </Suspense>
        </div>
      </main>
      <Footer />
    </div>
  )
}
