"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { changePassword, getCurrentUser, toggleTwoFactor } from "@/lib/auth"
import { Shield, Key, Smartphone, AlertTriangle } from "lucide-react"

export default function SecurityPage() {
  const { toast } = useToast()
  const currentUser = getCurrentUser()

  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmNewPassword, setConfirmNewPassword] = useState("")
  const [passwordError, setPasswordError] = useState("")

  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [qrCode, setQrCode] = useState("")
  const [twoFactorCode, setTwoFactorCode] = useState("")
  const [twoFactorError, setTwoFactorError] = useState("")

  const handleChangePassword = (e: React.FormEvent) => {
    e.preventDefault()
    setPasswordError("")

    if (newPassword !== confirmNewPassword) {
      setPasswordError("New passwords do not match.")
      return
    }
    if (newPassword.length < 6) {
      setPasswordError("New password must be at least 6 characters long.")
      return
    }

    // In a real app, you'd send currentPassword, newPassword to backend
    // For demo, we'll just simulate success if currentPassword matches demo user's
    if (currentUser?.email === "<EMAIL>" && currentPassword === "userpassword") {
      if (changePassword(currentUser.id, newPassword)) {
        toast({
          title: "Password Updated",
          description: "Your password has been successfully changed.",
        })
        setCurrentPassword("")
        setNewPassword("")
        setConfirmNewPassword("")
      } else {
        setPasswordError("Failed to change password. Please try again.")
      }
    } else {
      setPasswordError("Incorrect current password.")
    }
  }

  const handleToggleTwoFactor = (checked: boolean) => {
    if (!currentUser) return

    if (checked) {
      // Simulate generating QR code
      setQrCode(
        "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/Panes:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Panes",
      )
      setTwoFactorEnabled(true)
      toast({
        title: "2FA Setup Started",
        description: "Scan the QR code with your authenticator app.",
      })
    } else {
      setTwoFactorEnabled(false)
      setQrCode("")
      setTwoFactorCode("")
      toast({
        title: "2FA Disabled",
        description: "Two-factor authentication has been disabled.",
      })
    }

    if (toggleTwoFactor(currentUser.id, checked)) {
      // Success handled above
    } else {
      setTwoFactorError("Failed to toggle 2FA. Please try again.")
    }
  }

  const handleVerifyTwoFactor = () => {
    if (twoFactorCode.length === 6) {
      // Simulate verification
      toast({
        title: "2FA Enabled",
        description: "Two-factor authentication has been successfully enabled.",
      })
      setQrCode("")
      setTwoFactorCode("")
    } else {
      setTwoFactorError("Please enter a valid 6-digit code.")
    }
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="font-bebas text-3xl text-black">Security Settings</h1>
        <p className="font-poppins text-gray-600">Manage your account security and authentication methods.</p>
      </div>

      {/* Change Password */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5 text-primary-orange" />
            Change Password
          </CardTitle>
          <CardDescription>Update your password to keep your account secure.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleChangePassword} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Current Password</Label>
              <Input
                id="currentPassword"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
              <Input
                id="confirmNewPassword"
                type="password"
                value={confirmNewPassword}
                onChange={(e) => setConfirmNewPassword(e.target.value)}
                required
              />
            </div>
            {passwordError && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertTriangle className="h-4 w-4" />
                {passwordError}
              </div>
            )}
            <Button type="submit" className="bg-primary-orange hover:bg-orange-600">
              Update Password
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5 text-primary-orange" />
            Two-Factor Authentication
          </CardTitle>
          <CardDescription>
            Add an extra layer of security to your account with two-factor authentication.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="2fa-toggle">Enable Two-Factor Authentication</Label>
              <p className="text-sm text-gray-500">Use an authenticator app to generate verification codes.</p>
            </div>
            <Switch id="2fa-toggle" checked={twoFactorEnabled} onCheckedChange={handleToggleTwoFactor} />
          </div>

          {qrCode && (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <div className="text-center">
                <h3 className="font-medium mb-2">Scan QR Code</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                </p>
                <div className="flex justify-center mb-4">
                  <img src={qrCode || "/placeholder.svg"} alt="2FA QR Code" className="border rounded" />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="twoFactorCode">Enter verification code</Label>
                <Input
                  id="twoFactorCode"
                  type="text"
                  placeholder="000000"
                  value={twoFactorCode}
                  onChange={(e) => setTwoFactorCode(e.target.value)}
                  maxLength={6}
                />
              </div>
              {twoFactorError && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <AlertTriangle className="h-4 w-4" />
                  {twoFactorError}
                </div>
              )}
              <Button onClick={handleVerifyTwoFactor} className="w-full bg-primary-orange hover:bg-orange-600">
                Verify and Enable 2FA
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary-orange" />
            Security Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• Use a strong, unique password that you don't use elsewhere</li>
            <li>• Enable two-factor authentication for additional security</li>
            <li>• Never share your password or verification codes with anyone</li>
            <li>• Log out of your account when using shared or public computers</li>
            <li>• Regularly review your account activity for any suspicious behavior</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
