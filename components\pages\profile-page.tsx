"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Package,
  FileText,
  Settings,
  CreditCard,
  MapPin,
  Shield,
  LogOut,
  Trash2,
  Mail,
  Phone,
  Calendar,
  Edit,
} from "lucide-react"
import Link from "next/link"

interface Order {
  id: string
  date: string
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled"
  total: number
  items: number
}

interface Quote {
  id: string
  date: string
  status: "draft" | "sent" | "viewed" | "accepted" | "expired"
  total: number
  validUntil: string
}

export default function ProfilePage() {
  const [user] = useState({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "(*************",
    joinDate: "2023-01-15",
    totalOrders: 12,
    totalSpent: 4567.89,
  })

  const recentOrders: Order[] = [
    { id: "WO-2024-001", date: "2024-01-15", status: "delivered", total: 598.0, items: 2 },
    { id: "WO-2024-002", date: "2024-01-10", status: "shipped", total: 329.0, items: 1 },
    { id: "WO-2024-003", date: "2024-01-05", status: "processing", total: 1299.0, items: 3 },
  ]

  const recentQuotes: Quote[] = [
    { id: "Q-2024-001", date: "2024-01-20", status: "sent", total: 2450.0, validUntil: "2024-02-20" },
    { id: "Q-2024-002", date: "2024-01-18", status: "viewed", total: 890.0, validUntil: "2024-02-18" },
    { id: "Q-2024-003", date: "2024-01-15", status: "accepted", total: 1650.0, validUntil: "2024-02-15" },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
      case "accepted":
        return "bg-green-100 text-green-800"
      case "shipped":
      case "sent":
        return "bg-blue-100 text-blue-800"
      case "processing":
      case "viewed":
        return "bg-yellow-100 text-yellow-800"
      case "pending":
      case "draft":
        return "bg-gray-100 text-gray-800"
      case "cancelled":
      case "expired":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const menuItems = [
    {
      icon: Settings,
      label: "Manage Your Account",
      href: "/profile/account",
      description: "Update personal information and preferences",
    },
    { icon: FileText, label: "My Quotes", href: "/profile/quotes", description: "View and manage your window quotes" },
    {
      icon: Package,
      label: "Review Orders",
      href: "/profile/orders",
      description: "Track orders and view order history",
    },
    {
      icon: Mail,
      label: "Change Email or Password",
      href: "/profile/security",
      description: "Update login credentials",
    },
    {
      icon: MapPin,
      label: "Manage Saved Addresses",
      href: "/profile/addresses",
      description: "Add, edit, or remove addresses",
    },
    {
      icon: CreditCard,
      label: "Manage Saved Payment Methods",
      href: "/profile/payment-methods",
      description: "Manage credit cards and payment options",
    },
    { icon: Shield, label: "Two-Factor Authentication", href: "/profile/2fa", description: "Enhance account security" },
    { icon: LogOut, label: "Sign Out", href: "/sign-out", description: "Log out of your account" },
    {
      icon: Trash2,
      label: "Delete My Account",
      href: "/profile/delete-account",
      description: "Permanently delete your account",
      danger: true,
    },
  ]

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Profile Header */}
        <div className="mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="flex items-center gap-4 mb-4 md:mb-0">
                  <div className="w-16 h-16 bg-primary-orange rounded-full flex items-center justify-center text-white text-2xl font-bold">
                    {user.firstName[0]}
                    {user.lastName[0]}
                  </div>
                  <div>
                    <h1 className="font-bebas text-3xl text-gray-900">
                      {user.firstName} {user.lastName}
                    </h1>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        {user.email}
                      </span>
                      <span className="flex items-center gap-1">
                        <Phone className="w-4 h-4" />
                        {user.phone}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/profile/account">
                    <Button
                      variant="outline"
                      className="flex items-center gap-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                    >
                      <Edit className="w-4 h-4" />
                      Edit Profile
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Account Stats */}
          <div className="lg:col-span-2 space-y-8">
            <div className="grid md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Package className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{user.totalOrders}</div>
                  <div className="text-sm text-gray-600">Total Orders</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CreditCard className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">${user.totalSpent.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Total Spent</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Calendar className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{new Date(user.joinDate).getFullYear()}</div>
                  <div className="text-sm text-gray-600">Member Since</div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Orders */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Recent Orders</CardTitle>
                <Link href="/profile/orders">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                  >
                    View All
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                          <Package className="w-5 h-5 text-gray-600" />
                        </div>
                        <div>
                          <div className="font-semibold">{order.id}</div>
                          <div className="text-sm text-gray-600">
                            {new Date(order.date).toLocaleDateString()} • {order.items} items
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">${order.total.toFixed(2)}</div>
                        <Badge className={`text-xs ${getStatusColor(order.status)}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Quotes */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Recent Quotes</CardTitle>
                <Link href="/profile/quotes">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                  >
                    View All
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentQuotes.map((quote) => (
                    <div key={quote.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                          <FileText className="w-5 h-5 text-gray-600" />
                        </div>
                        <div>
                          <div className="font-semibold">{quote.id}</div>
                          <div className="text-sm text-gray-600">
                            Valid until {new Date(quote.validUntil).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">${quote.total.toFixed(2)}</div>
                        <Badge className={`text-xs ${getStatusColor(quote.status)}`}>
                          {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Account Menu */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Account Management</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {menuItems.map((item, index) => {
                    const Icon = item.icon
                    return (
                      <Link key={index} href={item.href}>
                        <div
                          className={`flex items-center gap-3 p-4 hover:bg-gray-50 transition-colors ${
                            item.danger ? "text-red-600 hover:bg-red-50" : "text-gray-700"
                          }`}
                        >
                          <Icon className="w-5 h-5" />
                          <div className="flex-1">
                            <div className="font-medium">{item.label}</div>
                            <div className="text-sm text-gray-500">{item.description}</div>
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/instant-quote">
                  <Button className="w-full bg-primary-orange hover:bg-orange-600 text-white">Get New Quote</Button>
                </Link>
                <Link href="/shop">
                  <Button
                    variant="outline"
                    className="w-full border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                  >
                    Browse Products
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button
                    variant="outline"
                    className="w-full border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                  >
                    Contact Support
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  )
}
