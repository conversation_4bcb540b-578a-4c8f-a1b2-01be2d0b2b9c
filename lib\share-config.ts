"use client"

interface ConfigData {
  configuration: string
  doorStyle: string
  doorGlass: string
  doorColor: string
  doorHandle: string
}

export async function shareDoorConfiguration(configData: ConfigData) {
  try {
    // Create a shareable URL with configuration parameters
    const baseUrl = window.location.origin
    const configParams = new URLSearchParams({
      config: configData.configuration,
      style: configData.doorStyle,
      glass: configData.doorGlass,
      color: configData.doorColor,
      handle: configData.doorHandle,
    })

    const shareUrl = `${baseUrl}/door-configurator?${configParams.toString()}`

    // Check if Web Share API is available
    if (navigator.share) {
      await navigator.share({
        title: "My Custom Door Configuration",
        text: "Check out my custom patio door configuration!",
        url: shareUrl,
      })
    } else {
      // Fallback: Copy to clipboard
      await navigator.clipboard.writeText(shareUrl)

      // Show success message
      const notification = document.createElement("div")
      notification.innerHTML = `
        <div style="
          position: fixed;
          top: 20px;
          right: 20px;
          background: #10b981;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          z-index: 1000;
          font-family: Arial, sans-serif;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        ">
          Configuration link copied to clipboard!
        </div>
      `

      document.body.appendChild(notification)

      setTimeout(() => {
        document.body.removeChild(notification)
      }, 3000)
    }
  } catch (error) {
    console.error("Error sharing configuration:", error)
    alert("Error sharing configuration. Please try again.")
  }
}
