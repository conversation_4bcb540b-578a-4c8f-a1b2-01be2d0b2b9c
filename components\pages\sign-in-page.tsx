"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { login, DEMO_USERS } from "@/lib/auth" // Import DEMO_USERS

export default function SignInPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const router = useRouter()

  const handleSignIn = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    const user = login(email, password)

    if (user) {
      if (user.role === "admin") {
        router.push("/admin")
      } else {
        router.push("/profile")
      }
    } else {
      setError("Invalid email or password.")
    }
  }

  const handleDemoLogin = (role: "admin" | "user") => {
    const demoUser = DEMO_USERS[role]
    setEmail(demoUser.email)
    setPassword(demoUser.password)
    // Automatically attempt login with demo credentials
    const user = login(demoUser.email, demoUser.password)
    if (user) {
      if (user.role === "admin") {
        router.push("/admin")
      } else {
        router.push("/profile")
      }
    } else {
      setError("Demo login failed. Please try again.")
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bebas text-primary-orange">Sign In</CardTitle>
          <CardDescription className="font-poppins text-gray-600">
            Access your account or create a new one.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignIn} className="space-y-6">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            {error && <p className="text-sm text-red-500">{error}</p>}
            <Button type="submit" className="w-full bg-primary-orange hover:bg-orange-600">
              Sign In
            </Button>
          </form>
          <div className="mt-6 text-center text-sm text-gray-600">
            Don&apos;t have an account?{" "}
            <Link href="/create-account" className="font-medium text-primary-orange hover:underline">
              Create Account
            </Link>
          </div>
          <div className="mt-6 space-y-2">
            <Button variant="outline" className="w-full bg-transparent" onClick={() => handleDemoLogin("user")}>
              Sign In as Demo User
            </Button>
            <Button variant="outline" className="w-full bg-transparent" onClick={() => handleDemoLogin("admin")}>
              Sign In as Demo Admin
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
