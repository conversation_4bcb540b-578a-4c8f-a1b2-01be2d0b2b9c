"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Star } from "lucide-react"
import { useState } from "react"

export default function ProductReviews() {
  const [rating, setRating] = useState(0)
  const [reviewText, setReviewText] = useState("")

  const handleStarClick = (index: number) => {
    setRating(index + 1)
  }

  const handleSubmitReview = () => {
    // In a real application, you would send this data to a backend API
    console.log("Submitting review:", { rating, reviewText })
    alert("Review submitted! (This is a demo, no actual submission)")
    setRating(0)
    setReviewText("")
  }

  return (
    <section className="py-12 px-4 md:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Customer Reviews</h2>

        {/* Existing Reviews (Placeholder) */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader>
              <CardTitle>Excellent Quality!</CardTitle>
              <CardDescription className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                ))}
                <span className="ml-2 text-sm text-gray-600">by John D.</span>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">
                {"I'm very impressed with the patio door. The quality is top-notch and it looks fantastic in my home."}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Great Value</CardTitle>
              <CardDescription className="flex items-center">
                {[...Array(4)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                ))}
                <Star className="w-4 h-4 text-gray-300" />
                <span className="ml-2 text-sm text-gray-600">by Jane S.</span>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">
                {"For the price, you can't beat the quality. Installation was straightforward too."}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Highly Recommend</CardTitle>
              <CardDescription className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                ))}
                <span className="ml-2 text-sm text-gray-600">by Michael B.</span>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700">
                {"The door arrived quickly and was exactly as described. Very happy with my purchase."}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Write a Review Section */}
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Write a Review</CardTitle>
            <CardDescription>Share your experience with this product.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <h3 className="text-lg font-medium mb-2">Your Rating</h3>
              <div className="flex space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-6 h-6 cursor-pointer ${
                      i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                    onClick={() => handleStarClick(i)}
                  />
                ))}
              </div>
            </div>
            <div className="mb-4">
              <h3 className="text-lg font-medium mb-2">Your Review</h3>
              <Textarea
                placeholder="Tell us about your experience..."
                value={reviewText}
                onChange={(e) => setReviewText(e.target.value)}
                rows={5}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSubmitReview} className="w-full bg-orange-500 hover:bg-orange-600">
              Submit Review
            </Button>
          </CardFooter>
        </Card>
      </div>
    </section>
  )
}
