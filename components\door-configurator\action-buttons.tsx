"use client"

import { motion } from "framer-motion"
import { Home, RotateCcw, RotateCw, ZoomIn, ZoomOut, ShoppingCart, Download, Share2 } from "lucide-react"
import { useDoorConfiguratorStore } from "@/lib/door-configurator-store"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { useCartStore } from "@/lib/cart-store"
import { useState } from "react"
import { generateDoorQuotePDF } from "@/lib/pdf-generator"
import { shareDoorConfiguration } from "@/lib/share-config"

export function ActionButtons() {
  const {
    reset,
    doorRotationY,
    setDoorRotationY,
    doorScale,
    setDoorScale,
    configuration,
    doorStyle,
    doorGlass,
    doorColor,
    doorHandle,
    currentStep,
  } = useDoorConfiguratorStore()
  const router = useRouter()
  const { addItem } = useCartStore()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [isSharing, setIsSharing] = useState(false)

  const isConfigurationComplete = configuration && doorStyle && doorGlass && doorColor && doorHandle

  const calculatePrice = () => {
    let basePrice = 850 // Base door price

    // Configuration pricing
    const configPricing = {
      "single-door": 0,
      "double-doors": 400,
      "single-door-right-sidelite": 200,
      "single-door-left-sidelite": 200,
      "single-door-two-sidelites": 350,
      "single-door-transom": 150,
      "single-door-left-sidelite-and-transom": 300,
      "single-door-right-sidelite-and-transom": 300,
      "single-door-two-sidelites-and-transom": 450,
      "double-doors-right-sidelite": 550,
      "double-doors-left-sidelite": 550,
      "double-doors-two-sidelites": 700,
      "double-doors-transom": 500,
      "single-door-right-extra-large-sidelite": 280,
      "single-door-left-extra-large-sidelite": 280,
      "single-door-left-extra-large-sidelite-and-transom": 380,
      "single-door-right-extra-large-sidelite-and-transom": 380,
    }

    // Door style pricing
    const stylePricing = {
      "without-door-lite": 0,
      "1/2-lite": 50,
      "3/4-lite": 75,
      "1/4-lite": 40,
      "vertical-door-lite": 60,
      "full-lite": 100,
      "3-lites": 80,
      "4-lites": 90,
      "centered-vertical-door-lite": 65,
    }

    // Glass pricing
    const glassPricing = {
      clear: 0,
      frosted: 30,
      decorative: 80,
      "energy-efficient": 120,
    }

    // Handle pricing
    const handlePricing = {
      standard: 0,
      premium: 45,
      modern: 35,
      classic: 25,
    }

    if (configuration) basePrice += configPricing[configuration] || 0
    if (doorStyle) basePrice += stylePricing[doorStyle] || 0
    if (doorGlass) basePrice += glassPricing[doorGlass] || 0
    if (doorHandle) basePrice += handlePricing[doorHandle] || 0

    return basePrice
  }

  const handleAddToCart = () => {
    if (!isConfigurationComplete) return

    const doorItem = {
      id: `door-${Date.now()}`,
      name: "Custom Patio Door",
      price: calculatePrice(),
      image: "/placeholder.svg?height=200&width=200",
      category: "doors",
      specifications: {
        configuration: configuration?.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
        doorStyle: doorStyle?.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
        doorGlass: doorGlass?.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
        doorColor: doorColor?.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
        doorHandle: doorHandle?.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
      },
    }

    addItem(doorItem)
  }

  const handleDownloadQuote = async () => {
    if (!isConfigurationComplete) return

    setIsGeneratingPDF(true)
    try {
      const quoteData = {
        configuration,
        doorStyle,
        doorGlass,
        doorColor,
        doorHandle,
        price: calculatePrice(),
        date: new Date().toLocaleDateString(),
        quoteNumber: `DQ-${Date.now()}`,
      }

      await generateDoorQuotePDF(quoteData)
    } catch (error) {
      console.error("Error generating PDF:", error)
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleShareConfig = async () => {
    if (!isConfigurationComplete) return

    setIsSharing(true)
    try {
      const configData = {
        configuration,
        doorStyle,
        doorGlass,
        doorColor,
        doorHandle,
      }

      await shareDoorConfiguration(configData)
    } catch (error) {
      console.error("Error sharing configuration:", error)
    } finally {
      setIsSharing(false)
    }
  }

  const handleRotate = () => {
    // Toggle between 0 and 180 degrees for a full 2D flip
    setDoorRotationY(doorRotationY === 0 ? 180 : 0)
  }

  const handleZoomIn = () => {
    setDoorScale(Math.min(doorScale + 0.1, 1.5)) // Max zoom 1.5
  }

  const handleZoomOut = () => {
    setDoorScale(Math.max(doorScale - 0.1, 0.8)) // Min zoom 0.8
  }

  const handleHome = () => {
    router.push("/")
  }

  const buttonClass =
    "w-12 h-12 bg-white border-2 border-orange-500 rounded-lg flex items-center justify-center text-orange-500 hover:bg-orange-50 transition-colors duration-200 shadow-md"

  return (
    <div className="w-20 bg-gray-50 flex flex-col items-center justify-start pt-8 space-y-4">
      <motion.button
        onClick={handleHome}
        className={buttonClass}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Home"
      >
        <Home className="w-5 h-5" />
      </motion.button>

      {/* Add to Cart */}
      <Button
        onClick={handleAddToCart}
        disabled={!isConfigurationComplete}
        className="w-12 h-12 p-0 bg-primary-orange hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed group relative"
        title="Add to Cart"
      >
        <ShoppingCart className="w-5 h-5 text-white" />
        {isConfigurationComplete && (
          <div className="absolute -right-16 top-1/2 -translate-y-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Add to Cart
          </div>
        )}
      </Button>

      {/* Download Quote */}
      <Button
        onClick={handleDownloadQuote}
        disabled={!isConfigurationComplete || isGeneratingPDF}
        className="w-12 h-12 p-0 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed group relative"
        title="Download Quote"
      >
        {isGeneratingPDF ? (
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        ) : (
          <Download className="w-5 h-5 text-white" />
        )}
        {isConfigurationComplete && !isGeneratingPDF && (
          <div className="absolute -right-20 top-1/2 -translate-y-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Download Quote
          </div>
        )}
      </Button>

      {/* Share Config */}
      <Button
        onClick={handleShareConfig}
        disabled={!isConfigurationComplete || isSharing}
        className="w-12 h-12 p-0 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed group relative"
        title="Share Configuration"
      >
        {isSharing ? (
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        ) : (
          <Share2 className="w-5 h-5 text-white" />
        )}
        {isConfigurationComplete && !isSharing && (
          <div className="absolute -right-24 top-1/2 -translate-y-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            Share Configuration
          </div>
        )}
      </Button>

      <motion.button
        onClick={reset}
        className={buttonClass}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Reset Configuration"
      >
        <RotateCcw className="w-5 h-5" />
      </motion.button>

      <motion.button
        onClick={handleRotate}
        className={buttonClass}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Rotate Door"
      >
        <RotateCw className="w-5 h-5" />
      </motion.button>

      <motion.button
        onClick={handleZoomIn}
        className={buttonClass}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Zoom In"
      >
        <ZoomIn className="w-5 h-5" />
      </motion.button>

      <motion.button
        onClick={handleZoomOut}
        className={buttonClass}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="Zoom Out"
      >
        <ZoomOut className="w-5 h-5" />
      </motion.button>
    </div>
  )
}
