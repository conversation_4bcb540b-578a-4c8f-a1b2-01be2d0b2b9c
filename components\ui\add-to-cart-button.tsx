"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ShoppingCart, Check } from "lucide-react"
import { useCartStore, type CartItem } from "@/lib/cart-store"

interface AddToCartButtonProps {
  item: Omit<CartItem, "quantity">
  className?: string
  size?: "sm" | "default" | "lg"
  variant?: "default" | "outline" | "secondary"
  children?: React.ReactNode
}

export default function AddToCartButton({
  item,
  className = "",
  size = "default",
  variant = "default",
  children,
}: AddToCartButtonProps) {
  const [isAdding, setIsAdding] = useState(false)
  const [justAdded, setJustAdded] = useState(false)
  const { addItem, setIsOpen } = useCartStore()

  const handleAddToCart = async () => {
    setIsAdding(true)

    // Add item to cart
    addItem(item)

    // Show adding animation
    await new Promise((resolve) => setTimeout(resolve, 300))

    setIsAdding(false)
    setJustAdded(true)

    // Show success state briefly
    setTimeout(() => {
      setJustAdded(false)
    }, 1500)

    // Optional: Open cart preview after adding
    setTimeout(() => {
      setIsOpen(true)
    }, 500)
  }

  return (
    <Button
      onClick={handleAddToCart}
      disabled={isAdding}
      size={size}
      variant={variant}
      className={`relative overflow-hidden transition-all duration-300 ${
        justAdded ? "bg-green-600 hover:bg-green-700" : ""
      } ${className}`}
    >
      <div
        className={`flex items-center gap-2 transition-transform duration-300 ${isAdding ? "scale-95" : "scale-100"}`}
      >
        {justAdded ? (
          <>
            <Check className="w-4 h-4" />
            <span>Added to Cart!</span>
          </>
        ) : (
          <>
            <ShoppingCart className={`w-4 h-4 transition-transform duration-300 ${isAdding ? "animate-bounce" : ""}`} />
            <span>{children || "Add to Cart"}</span>
          </>
        )}
      </div>

      {/* Loading overlay */}
      {isAdding && (
        <div className="absolute inset-0 bg-white bg-opacity-20 flex items-center justify-center">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {/* Success animation */}
      {justAdded && <div className="absolute inset-0 bg-green-500 bg-opacity-20 animate-pulse" />}
    </Button>
  )
}
