"use client"

import type React from "react"

import { useState } from "react"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import FixedElements from "@/components/layout/fixed-elements"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Calendar, Clock, MapPin, Phone, User, Mail, Home } from "lucide-react"

interface TimeSlot {
  time: string
  available: boolean
}

interface AppointmentData {
  type: "estimate" | "showroom"
  date: string
  time: string
  name: string
  email: string
  phone: string
  address: string
  notes: string
}

export default function AppointmentSchedulerPage() {
  const [selectedType, setSelectedType] = useState<"estimate" | "showroom">("estimate")
  const [selectedDate, setSelectedDate] = useState("")
  const [selectedTime, setSelectedTime] = useState("")
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState(false)

  // Generate available dates (next 30 days, excluding weekends)
  const generateAvailableDates = () => {
    const dates = []
    const today = new Date()

    for (let i = 1; i <= 30; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)

      // Skip weekends
      if (date.getDay() !== 0 && date.getDay() !== 6) {
        dates.push({
          value: date.toISOString().split("T")[0],
          label: date.toLocaleDateString("en-US", {
            weekday: "long",
            month: "long",
            day: "numeric",
          }),
        })
      }
    }

    return dates
  }

  // Generate time slots
  const generateTimeSlots = (): TimeSlot[] => {
    const slots = []
    const startHour = 9
    const endHour = 17

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += 60) {
        const time = `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`
        const displayTime = new Date(`2000-01-01T${time}`).toLocaleTimeString("en-US", {
          hour: "numeric",
          minute: "2-digit",
          hour12: true,
        })

        // Simulate some slots being unavailable
        const available = Math.random() > 0.3

        slots.push({
          time: displayTime,
          available,
        })
      }
    }

    return slots
  }

  const availableDates = generateAvailableDates()
  const timeSlots = generateTimeSlots()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    const appointmentData: AppointmentData = {
      type: selectedType,
      date: selectedDate,
      time: selectedTime,
      ...formData,
    }

    console.log("Appointment booked:", appointmentData)
    setIsSubmitting(false)
    setShowConfirmation(true)
  }

  const resetForm = () => {
    setSelectedType("estimate")
    setSelectedDate("")
    setSelectedTime("")
    setFormData({
      name: "",
      email: "",
      phone: "",
      address: "",
      notes: "",
    })
    setShowConfirmation(false)
  }

  if (showConfirmation) {
    return (
      <div className="min-h-screen bg-white">
        <Header />

        <main className="py-12 bg-gray-50">
          <div className="max-w-2xl mx-auto px-4">
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Calendar className="w-8 h-8 text-green-600" />
              </div>

              <h1 className="text-3xl font-bebas text-black mb-4">Appointment Confirmed!</h1>

              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h2 className="font-bebas text-xl text-black mb-4">Appointment Details</h2>
                <div className="space-y-3 text-left">
                  <div className="flex items-center space-x-3">
                    <Home className="w-5 h-5 text-primary-orange" />
                    <span className="font-medium">
                      {selectedType === "estimate" ? "In-Home Estimate" : "Showroom Visit"}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-primary-orange" />
                    <span>{availableDates.find((d) => d.value === selectedDate)?.label}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="w-5 h-5 text-primary-orange" />
                    <span>{selectedTime}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <User className="w-5 h-5 text-primary-orange" />
                    <span>{formData.name}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-primary-orange" />
                    <span>{formData.email}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-primary-orange" />
                    <span>{formData.phone}</span>
                  </div>
                </div>
              </div>

              <p className="text-gray-600 mb-6">
                A confirmation email has been sent to {formData.email}. Our team will contact you 24 hours before your
                appointment.
              </p>

              <div className="space-y-3">
                <Button onClick={resetForm} className="w-full bg-primary-orange hover:bg-orange-700 text-white">
                  Book Another Appointment
                </Button>
                <Button onClick={() => (window.location.href = "/")} variant="outline" className="w-full">
                  Return to Home
                </Button>
              </div>
            </div>
          </div>
        </main>

        <Footer />
        <FixedElements />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="py-12 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bebas text-black mb-4">Schedule an Appointment</h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Book a free consultation with our window experts. Choose between an in-home estimate or visit our showroom
              to see our products firsthand.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            {/* Appointment Type Selection */}
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bebas text-black mb-4">Select Appointment Type</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => setSelectedType("estimate")}
                  className={`p-6 rounded-lg border-2 transition-all ${
                    selectedType === "estimate"
                      ? "border-primary-orange bg-orange-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <Home className="w-8 h-8 text-primary-orange mx-auto mb-3" />
                  <h3 className="font-bebas text-lg text-black mb-2">In-Home Estimate</h3>
                  <p className="text-gray-600 text-sm">
                    Our expert will visit your home to provide accurate measurements and personalized recommendations.
                  </p>
                </button>

                <button
                  onClick={() => setSelectedType("showroom")}
                  className={`p-6 rounded-lg border-2 transition-all ${
                    selectedType === "showroom"
                      ? "border-primary-orange bg-orange-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <MapPin className="w-8 h-8 text-primary-orange mx-auto mb-3" />
                  <h3 className="font-bebas text-lg text-black mb-2">Showroom Visit</h3>
                  <p className="text-gray-600 text-sm">
                    Visit our showroom to see our products, materials, and get expert advice from our team.
                  </p>
                </button>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              {/* Date Selection */}
              <div className="mb-6">
                <h3 className="text-xl font-bebas text-black mb-4">Select Date</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {availableDates.slice(0, 12).map((date) => (
                    <button
                      key={date.value}
                      type="button"
                      onClick={() => setSelectedDate(date.value)}
                      className={`p-3 rounded-lg border text-sm transition-all ${
                        selectedDate === date.value
                          ? "border-primary-orange bg-orange-50 text-primary-orange"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      {date.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Time Selection */}
              {selectedDate && (
                <div className="mb-6">
                  <h3 className="text-xl font-bebas text-black mb-4">Select Time</h3>
                  <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {timeSlots.map((slot, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => slot.available && setSelectedTime(slot.time)}
                        disabled={!slot.available}
                        className={`p-3 rounded-lg border text-sm transition-all ${
                          selectedTime === slot.time
                            ? "border-primary-orange bg-orange-50 text-primary-orange"
                            : slot.available
                              ? "border-gray-200 hover:border-gray-300"
                              : "border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed"
                        }`}
                      >
                        {slot.time}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Information */}
              {selectedDate && selectedTime && (
                <div className="space-y-6">
                  <h3 className="text-xl font-bebas text-black">Contact Information</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                      </label>
                      <Input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                        required
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                        required
                        placeholder="Enter your email"
                      />
                    </div>

                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number *
                      </label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                        required
                        placeholder="(*************"
                      />
                    </div>

                    {selectedType === "estimate" && (
                      <div>
                        <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                          Home Address *
                        </label>
                        <Input
                          id="address"
                          type="text"
                          value={formData.address}
                          onChange={(e) => setFormData((prev) => ({ ...prev, address: e.target.value }))}
                          required
                          placeholder="Enter your address"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                      Additional Notes (Optional)
                    </label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
                      placeholder="Tell us about your project, specific requirements, or any questions you have..."
                      rows={4}
                    />
                  </div>

                  <div className="pt-6">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary-orange hover:bg-orange-700 text-white py-3"
                    >
                      {isSubmitting ? "Booking Appointment..." : "Book Appointment"}
                    </Button>
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </main>

      <Footer />
      <FixedElements />
    </div>
  )
}
