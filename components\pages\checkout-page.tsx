"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { CreditCard, Lock, Truck, MapPin, User, Shield } from "lucide-react"
import Link from "next/link"

interface CartItem {
  id: number
  name: string
  category: string
  price: number
  quantity: number
  image: string
  options: string[]
}

interface FormData {
  email: string
  firstName: string
  lastName: string
  company: string
  address: string
  apartment: string
  city: string
  state: string
  zipCode: string
  phone: string
  billingAddress: {
    firstName: string
    lastName: string
    company: string
    address: string
    apartment: string
    city: string
    state: string
    zipCode: string
  }
  paymentMethod: string
  cardNumber: string
  expiryDate: string
  cvv: string
  nameOnCard: string
  specialInstructions: string
  sameAsBilling: boolean
  saveInfo: boolean
  newsletter: boolean
}

interface FormErrors {
  [key: string]: string
}

export default function CheckoutPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState<FormData>({
    email: "",
    firstName: "",
    lastName: "",
    company: "",
    address: "",
    apartment: "",
    city: "",
    state: "",
    zipCode: "",
    phone: "",
    billingAddress: {
      firstName: "",
      lastName: "",
      company: "",
      address: "",
      apartment: "",
      city: "",
      state: "",
      zipCode: "",
    },
    paymentMethod: "credit",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    nameOnCard: "",
    specialInstructions: "",
    sameAsBilling: true,
    saveInfo: false,
    newsletter: false,
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isProcessing, setIsProcessing] = useState(false)

  // Sample cart items
  const cartItems: CartItem[] = [
    {
      id: 1,
      name: "Double Hung Window",
      category: "Windows",
      price: 299,
      quantity: 2,
      image: "/placeholder.svg?height=80&width=80",
      options: ["White Frame", "Low-E Glass", "Standard Screen"],
    },
    {
      id: 2,
      name: "Casement Window",
      category: "Windows",
      price: 329,
      quantity: 1,
      image: "/placeholder.svg?height=80&width=80",
      options: ["Bronze Frame", "Triple Pane", "FlexScreen"],
    },
  ]

  const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
  const shipping = 89
  const tax = subtotal * 0.08
  const total = subtotal + shipping + tax

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case "email":
        if (!value) return "Email is required"
        if (!/\S+@\S+\.\S+/.test(value)) return "Email is invalid"
        return ""
      case "firstName":
      case "lastName":
        if (!value) return `${name === "firstName" ? "First" : "Last"} name is required`
        if (value.length < 2) return "Name must be at least 2 characters"
        return ""
      case "address":
        if (!value) return "Address is required"
        return ""
      case "city":
        if (!value) return "City is required"
        return ""
      case "state":
        if (!value) return "State is required"
        return ""
      case "zipCode":
        if (!value) return "ZIP code is required"
        if (!/^\d{5}(-\d{4})?$/.test(value)) return "Invalid ZIP code format"
        return ""
      case "phone":
        if (!value) return "Phone number is required"
        if (!/^$$\d{3}$$ \d{3}-\d{4}$/.test(value)) return "Invalid phone format"
        return ""
      case "cardNumber":
        if (!value) return "Card number is required"
        if (!/^\d{4} \d{4} \d{4} \d{4}$/.test(value)) return "Invalid card number format"
        return ""
      case "expiryDate":
        if (!value) return "Expiry date is required"
        if (!/^\d{2}\/\d{2}$/.test(value)) return "Invalid expiry format (MM/YY)"
        return ""
      case "cvv":
        if (!value) return "CVV is required"
        if (!/^\d{3,4}$/.test(value)) return "Invalid CVV"
        return ""
      case "nameOnCard":
        if (!value) return "Name on card is required"
        return ""
      default:
        return ""
    }
  }

  const handleInputChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }))
    }
  }

  const handleBillingChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      billingAddress: { ...prev.billingAddress, [name]: value },
    }))
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "")
    const matches = v.match(/\d{4,16}/g)
    const match = (matches && matches[0]) || ""
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(" ")
    } else {
      return v
    }
  }

  const formatPhone = (value: string) => {
    const phoneNumber = value.replace(/[^\d]/g, "")
    const phoneNumberLength = phoneNumber.length
    if (phoneNumberLength < 4) return phoneNumber
    if (phoneNumberLength < 7) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`
    }
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`
  }

  const validateStep = (step: number): boolean => {
    const newErrors: FormErrors = {}

    if (step === 1) {
      // Contact information validation
      newErrors.email = validateField("email", formData.email)
      newErrors.firstName = validateField("firstName", formData.firstName)
      newErrors.lastName = validateField("lastName", formData.lastName)
      newErrors.phone = validateField("phone", formData.phone)
    } else if (step === 2) {
      // Shipping address validation
      newErrors.address = validateField("address", formData.address)
      newErrors.city = validateField("city", formData.city)
      newErrors.state = validateField("state", formData.state)
      newErrors.zipCode = validateField("zipCode", formData.zipCode)

      // Billing address validation if different
      if (!formData.sameAsBilling) {
        newErrors["billingAddress.firstName"] = validateField("firstName", formData.billingAddress.firstName)
        newErrors["billingAddress.lastName"] = validateField("lastName", formData.billingAddress.lastName)
        newErrors["billingAddress.address"] = validateField("address", formData.billingAddress.address)
        newErrors["billingAddress.city"] = validateField("city", formData.billingAddress.city)
        newErrors["billingAddress.state"] = validateField("state", formData.billingAddress.state)
        newErrors["billingAddress.zipCode"] = validateField("zipCode", formData.billingAddress.zipCode)
      }
    } else if (step === 3) {
      // Payment validation
      if (formData.paymentMethod === "credit") {
        newErrors.cardNumber = validateField("cardNumber", formData.cardNumber)
        newErrors.expiryDate = validateField("expiryDate", formData.expiryDate)
        newErrors.cvv = validateField("cvv", formData.cvv)
        newErrors.nameOnCard = validateField("nameOnCard", formData.nameOnCard)
      }
    }

    // Filter out empty errors
    const filteredErrors = Object.fromEntries(Object.entries(newErrors).filter(([_, value]) => value !== ""))

    setErrors(filteredErrors)
    return Object.keys(filteredErrors).length === 0
  }

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, 4))
    }
  }

  const handlePrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(3)) return

    setIsProcessing(true)

    // Simulate payment processing
    try {
      await new Promise((resolve) => setTimeout(resolve, 3000))
      setCurrentStep(4) // Success step
    } catch (error) {
      console.error("Payment failed:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const steps = [
    { number: 1, title: "Contact", icon: User },
    { number: 2, title: "Shipping", icon: Truck },
    { number: 3, title: "Payment", icon: CreditCard },
    { number: 4, title: "Complete", icon: Shield },
  ]

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            {steps.map((step, index) => {
              const Icon = step.icon
              const isActive = currentStep === step.number
              const isCompleted = currentStep > step.number

              return (
                <div key={step.number} className="flex items-center">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isCompleted
                        ? "bg-green-500 border-green-500 text-white"
                        : isActive
                          ? "bg-primary-orange border-primary-orange text-white"
                          : "bg-white border-gray-300 text-gray-400"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                  </div>
                  <span
                    className={`ml-2 text-sm font-medium ${
                      isActive ? "text-primary-orange" : isCompleted ? "text-green-500" : "text-gray-400"
                    }`}
                  >
                    {step.title}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${isCompleted ? "bg-green-500" : "bg-gray-300"}`} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={errors.email ? "border-red-500" : ""}
                      placeholder="<EMAIL>"
                    />
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange("firstName", e.target.value)}
                        className={errors.firstName ? "border-red-500" : ""}
                        placeholder="John"
                      />
                      {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange("lastName", e.target.value)}
                        className={errors.lastName ? "border-red-500" : ""}
                        placeholder="Doe"
                      />
                      {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="company">Company (Optional)</Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => handleInputChange("company", e.target.value)}
                      placeholder="Your Company Name"
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", formatPhone(e.target.value))}
                      className={errors.phone ? "border-red-500" : ""}
                      placeholder="(*************"
                      maxLength={14}
                    />
                    {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="newsletter"
                      checked={formData.newsletter}
                      onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, newsletter: !!checked }))}
                    />
                    <Label htmlFor="newsletter" className="text-sm">
                      Subscribe to our newsletter for updates and special offers
                    </Label>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 2 && (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="w-5 h-5" />
                      Shipping Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="address">Street Address *</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => handleInputChange("address", e.target.value)}
                        className={errors.address ? "border-red-500" : ""}
                        placeholder="123 Main Street"
                      />
                      {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
                    </div>

                    <div>
                      <Label htmlFor="apartment">Apartment, Suite, etc. (Optional)</Label>
                      <Input
                        id="apartment"
                        value={formData.apartment}
                        onChange={(e) => handleInputChange("apartment", e.target.value)}
                        placeholder="Apt 4B"
                      />
                    </div>

                    <div className="grid md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="city">City *</Label>
                        <Input
                          id="city"
                          value={formData.city}
                          onChange={(e) => handleInputChange("city", e.target.value)}
                          className={errors.city ? "border-red-500" : ""}
                          placeholder="New York"
                        />
                        {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
                      </div>
                      <div>
                        <Label htmlFor="state">State *</Label>
                        <Select value={formData.state} onValueChange={(value) => handleInputChange("state", value)}>
                          <SelectTrigger className={errors.state ? "border-red-500" : ""}>
                            <SelectValue placeholder="Select state" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="AL">Alabama</SelectItem>
                            <SelectItem value="AK">Alaska</SelectItem>
                            <SelectItem value="AZ">Arizona</SelectItem>
                            <SelectItem value="AR">Arkansas</SelectItem>
                            <SelectItem value="CA">California</SelectItem>
                            <SelectItem value="CO">Colorado</SelectItem>
                            <SelectItem value="CT">Connecticut</SelectItem>
                            <SelectItem value="DE">Delaware</SelectItem>
                            <SelectItem value="FL">Florida</SelectItem>
                            <SelectItem value="GA">Georgia</SelectItem>
                            <SelectItem value="NY">New York</SelectItem>
                            <SelectItem value="TX">Texas</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.state && <p className="text-red-500 text-sm mt-1">{errors.state}</p>}
                      </div>
                      <div>
                        <Label htmlFor="zipCode">ZIP Code *</Label>
                        <Input
                          id="zipCode"
                          value={formData.zipCode}
                          onChange={(e) => handleInputChange("zipCode", e.target.value)}
                          className={errors.zipCode ? "border-red-500" : ""}
                          placeholder="10001"
                        />
                        {errors.zipCode && <p className="text-red-500 text-sm mt-1">{errors.zipCode}</p>}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="specialInstructions">Special Delivery Instructions (Optional)</Label>
                      <Textarea
                        id="specialInstructions"
                        value={formData.specialInstructions}
                        onChange={(e) => handleInputChange("specialInstructions", e.target.value)}
                        placeholder="Leave at front door, call upon arrival, etc."
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      Billing Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="sameAsBilling"
                        checked={formData.sameAsBilling}
                        onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, sameAsBilling: !!checked }))}
                      />
                      <Label htmlFor="sameAsBilling">Same as shipping address</Label>
                    </div>

                    {!formData.sameAsBilling && (
                      <div className="space-y-4">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="billingFirstName">First Name *</Label>
                            <Input
                              id="billingFirstName"
                              value={formData.billingAddress.firstName}
                              onChange={(e) => handleBillingChange("firstName", e.target.value)}
                              className={errors["billingAddress.firstName"] ? "border-red-500" : ""}
                            />
                            {errors["billingAddress.firstName"] && (
                              <p className="text-red-500 text-sm mt-1">{errors["billingAddress.firstName"]}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="billingLastName">Last Name *</Label>
                            <Input
                              id="billingLastName"
                              value={formData.billingAddress.lastName}
                              onChange={(e) => handleBillingChange("lastName", e.target.value)}
                              className={errors["billingAddress.lastName"] ? "border-red-500" : ""}
                            />
                            {errors["billingAddress.lastName"] && (
                              <p className="text-red-500 text-sm mt-1">{errors["billingAddress.lastName"]}</p>
                            )}
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="billingAddress">Street Address *</Label>
                          <Input
                            id="billingAddress"
                            value={formData.billingAddress.address}
                            onChange={(e) => handleBillingChange("address", e.target.value)}
                            className={errors["billingAddress.address"] ? "border-red-500" : ""}
                          />
                          {errors["billingAddress.address"] && (
                            <p className="text-red-500 text-sm mt-1">{errors["billingAddress.address"]}</p>
                          )}
                        </div>

                        <div className="grid md:grid-cols-3 gap-4">
                          <div>
                            <Label htmlFor="billingCity">City *</Label>
                            <Input
                              id="billingCity"
                              value={formData.billingAddress.city}
                              onChange={(e) => handleBillingChange("city", e.target.value)}
                              className={errors["billingAddress.city"] ? "border-red-500" : ""}
                            />
                            {errors["billingAddress.city"] && (
                              <p className="text-red-500 text-sm mt-1">{errors["billingAddress.city"]}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="billingState">State *</Label>
                            <Select
                              value={formData.billingAddress.state}
                              onValueChange={(value) => handleBillingChange("state", value)}
                            >
                              <SelectTrigger className={errors["billingAddress.state"] ? "border-red-500" : ""}>
                                <SelectValue placeholder="Select state" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="NY">New York</SelectItem>
                                <SelectItem value="CA">California</SelectItem>
                                <SelectItem value="TX">Texas</SelectItem>
                                <SelectItem value="FL">Florida</SelectItem>
                              </SelectContent>
                            </Select>
                            {errors["billingAddress.state"] && (
                              <p className="text-red-500 text-sm mt-1">{errors["billingAddress.state"]}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="billingZipCode">ZIP Code *</Label>
                            <Input
                              id="billingZipCode"
                              value={formData.billingAddress.zipCode}
                              onChange={(e) => handleBillingChange("zipCode", e.target.value)}
                              className={errors["billingAddress.zipCode"] ? "border-red-500" : ""}
                            />
                            {errors["billingAddress.zipCode"] && (
                              <p className="text-red-500 text-sm mt-1">{errors["billingAddress.zipCode"]}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}

            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="w-5 h-5" />
                    Payment Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <RadioGroup
                    value={formData.paymentMethod}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, paymentMethod: value }))}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="credit" id="credit" />
                      <Label htmlFor="credit" className="flex items-center gap-2">
                        <CreditCard className="w-4 h-4" />
                        Credit Card
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="paypal" id="paypal" />
                      <Label htmlFor="paypal">PayPal</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="financing" id="financing" />
                      <Label htmlFor="financing">Financing Options</Label>
                    </div>
                  </RadioGroup>

                  {formData.paymentMethod === "credit" && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="cardNumber">Card Number *</Label>
                        <Input
                          id="cardNumber"
                          value={formData.cardNumber}
                          onChange={(e) => handleInputChange("cardNumber", formatCardNumber(e.target.value))}
                          className={errors.cardNumber ? "border-red-500" : ""}
                          placeholder="1234 5678 9012 3456"
                          maxLength={19}
                        />
                        {errors.cardNumber && <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>}
                      </div>

                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="expiryDate">Expiry Date *</Label>
                          <Input
                            id="expiryDate"
                            value={formData.expiryDate}
                            onChange={(e) => {
                              let value = e.target.value.replace(/\D/g, "")
                              if (value.length >= 2) {
                                value = value.substring(0, 2) + "/" + value.substring(2, 4)
                              }
                              handleInputChange("expiryDate", value)
                            }}
                            className={errors.expiryDate ? "border-red-500" : ""}
                            placeholder="MM/YY"
                            maxLength={5}
                          />
                          {errors.expiryDate && <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>}
                        </div>
                        <div>
                          <Label htmlFor="cvv">CVV *</Label>
                          <Input
                            id="cvv"
                            value={formData.cvv}
                            onChange={(e) => handleInputChange("cvv", e.target.value.replace(/\D/g, ""))}
                            className={errors.cvv ? "border-red-500" : ""}
                            placeholder="123"
                            maxLength={4}
                          />
                          {errors.cvv && <p className="text-red-500 text-sm mt-1">{errors.cvv}</p>}
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="nameOnCard">Name on Card *</Label>
                        <Input
                          id="nameOnCard"
                          value={formData.nameOnCard}
                          onChange={(e) => handleInputChange("nameOnCard", e.target.value)}
                          className={errors.nameOnCard ? "border-red-500" : ""}
                          placeholder="John Doe"
                        />
                        {errors.nameOnCard && <p className="text-red-500 text-sm mt-1">{errors.nameOnCard}</p>}
                      </div>
                    </div>
                  )}

                  {formData.paymentMethod === "paypal" && (
                    <div className="text-center py-8">
                      <p className="text-gray-600 mb-4">You will be redirected to PayPal to complete your payment</p>
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">Continue with PayPal</Button>
                    </div>
                  )}

                  {formData.paymentMethod === "financing" && (
                    <div className="space-y-4">
                      <p className="text-gray-600">Choose from our financing options:</p>
                      <div className="grid gap-4">
                        <Card className="p-4 border-2 border-primary-orange">
                          <h4 className="font-semibold">12 Months Same as Cash</h4>
                          <p className="text-sm text-gray-600">0% APR for qualified buyers</p>
                        </Card>
                        <Card className="p-4">
                          <h4 className="font-semibold">Extended Payment Plan</h4>
                          <p className="text-sm text-gray-600">Low monthly payments up to 60 months</p>
                        </Card>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="saveInfo"
                      checked={formData.saveInfo}
                      onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, saveInfo: !!checked }))}
                    />
                    <Label htmlFor="saveInfo" className="text-sm">
                      Save payment information for future purchases
                    </Label>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Lock className="w-4 h-4" />
                      Your payment information is encrypted and secure
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {currentStep === 4 && (
              <Card>
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  <h2 className="font-bebas text-3xl text-gray-900 mb-4">Order Complete!</h2>
                  <p className="text-lg text-gray-600 mb-6">
                    Thank you for your purchase. Your order #WO-2024-001 has been confirmed.
                  </p>
                  <div className="space-y-4">
                    <p className="text-sm text-gray-600">A confirmation email has been sent to {formData.email}</p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Link href="/profile/orders">
                        <Button className="bg-primary-orange hover:bg-orange-600 text-white">View Order Status</Button>
                      </Link>
                      <Link href="/shop">
                        <Button
                          variant="outline"
                          className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                        >
                          Continue Shopping
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Navigation Buttons */}
            {currentStep < 4 && (
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevStep}
                  disabled={currentStep === 1}
                  className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white bg-transparent"
                >
                  Previous
                </Button>

                {currentStep < 3 ? (
                  <Button onClick={handleNextStep} className="bg-primary-orange hover:bg-orange-600 text-white">
                    Continue
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isProcessing}
                    className="bg-primary-orange hover:bg-orange-600 text-white"
                  >
                    {isProcessing ? "Processing..." : "Complete Order"}
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {cartItems.map((item) => (
                  <div key={item.id} className="flex gap-4">
                    <img
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold text-sm">{item.name}</h4>
                      <p className="text-xs text-gray-600">{item.category}</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {item.options.map((option, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {option}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-sm">Qty: {item.quantity}</span>
                        <span className="font-semibold">${item.price * item.quantity}</span>
                      </div>
                    </div>
                  </div>
                ))}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>${subtotal}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Shipping</span>
                    <span>${shipping}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax</span>
                    <span>${tax.toFixed(2)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>${total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Free shipping</strong> on orders over $500!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  )
}
