"use client"

import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import PatioDoorHero from "@/components/sections/patio-door-hero"
import PatioDoorDescription from "@/components/sections/patio-door-description"
import PatioDoorSpecifications from "@/components/sections/patio-door-specifications"
import PatioDoorPolicies from "@/components/sections/patio-door-policies"
import PatioDoorQA from "@/components/sections/patio-door-qa"
import ProductReviews from "@/components/sections/product-reviews"
import FixedElements from "@/components/layout/fixed-elements"

const PatioDoorProductPage = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="flex-1">
        <PatioDoorHero />
        <PatioDoorDescription />
        <PatioDoorSpecifications />
        <PatioDoorPolicies />
        <PatioDoorQA />
        <ProductReviews productName="Patio Doors" />
      </main>

      <FixedElements />
      <Footer />
    </div>
  )
}

export default PatioDoorProductPage
