"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore, type DoorHandle } from "@/lib/door-configurator-store"
import { Search } from "lucide-react"

const handleOptions: { key: DoorHandle; label: string; description: string }[] = [
  { key: "standard", label: "Standard Handle", description: "Basic door handle" },
  { key: "premium", label: "Premium Handle", description: "High-quality finish" },
  { key: "modern", label: "Modern Handle", description: "Contemporary design" },
  { key: "classic", label: "Classic Handle", description: "Traditional style" },
]

export function HandleStep() {
  const { doorHandle, setDoorHandle } = useDoorConfiguratorStore()

  const renderHandlePreview = (handle: DoorHandle) => {
    let handleColor = "bg-gray-800"
    if (handle === "premium") handleColor = "bg-yellow-600"
    else if (handle === "modern") handleColor = "bg-gray-600"
    else if (handle === "classic") handleColor = "bg-amber-700"

    return (
      <div className="w-full h-24 bg-gray-100 rounded border border-gray-300 relative flex items-center justify-center">
        <div className={`w-8 h-16 ${handleColor} rounded-full shadow-md`} />
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">DOOR HANDLE</h2>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {handleOptions.map((handle) => (
          <motion.button
            key={handle.key}
            onClick={() => setDoorHandle(handle.key)}
            className={`
              p-4 rounded-lg border-2 text-left transition-all duration-200
              ${
                doorHandle === handle.key
                  ? "border-orange-500 bg-orange-50"
                  : "border-gray-200 bg-white hover:border-gray-300"
              }
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {renderHandlePreview(handle.key)}
            <h3 className="font-medium text-gray-900 mt-3">{handle.label}</h3>
            <p className="text-sm text-gray-600 mt-1">{handle.description}</p>
          </motion.button>
        ))}
      </div>
    </div>
  )
}
