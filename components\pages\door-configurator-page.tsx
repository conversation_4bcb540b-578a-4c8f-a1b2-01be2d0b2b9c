"use client"

import { ConfiguratorHeader } from "@/components/door-configurator/configurator-header"
import { ProgressBar } from "@/components/door-configurator/progress-bar"
import { DoorPreview } from "@/components/door-configurator/door-preview"
import { ActionButtons } from "@/components/door-configurator/action-buttons"
import { StepContent } from "@/components/door-configurator/step-content"
import { NavigationButtons } from "@/components/door-configurator/navigation-buttons"
import { ConfiguratorFooter } from "@/components/door-configurator/configurator-footer"

export default function DoorConfiguratorPage() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <ConfiguratorHeader />
      <ProgressBar />
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Options */}
        <div className="w-[400px] bg-gray-50 border-r border-gray-200 overflow-y-auto">
          <StepContent />
        </div>

        {/* Center - Door Preview */}
        <div className="flex-1 flex items-center justify-center bg-white relative">
          <DoorPreview />
        </div>

        {/* Right Sidebar - Action Buttons */}
        <div className="w-[80px] bg-white border-l border-gray-200 flex flex-col items-center justify-center py-6 space-y-4">
          <ActionButtons />
        </div>
      </div>
      <NavigationButtons />
      <ConfiguratorFooter />
    </div>
  )
}
