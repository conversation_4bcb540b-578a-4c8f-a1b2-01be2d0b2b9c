import { create } from "zustand"
import { persist } from "zustand/middleware"

export type DoorConfiguration =
  | "single-door"
  | "double-doors"
  | "single-door-right-sidelite"
  | "single-door-left-sidelite"
  | "single-door-two-sidelites"
  | "single-door-transom"
  | "single-door-left-sidelite-and-transom"
  | "single-door-right-sidelite-and-transom"
  | "single-door-two-sidelites-and-transom"
  | "double-doors-right-sidelite"
  | "double-doors-left-sidelite"
  | "double-doors-two-sidelites"
  | "double-doors-transom"
  | "single-door-right-extra-large-sidelite"
  | "single-door-left-extra-large-sidelite"
  | "single-door-left-extra-large-sidelite-and-transom"
  | "single-door-right-extra-large-sidelite-and-transom"
export type DoorStyle =
  | "without-door-lite"
  | "1/2-lite"
  | "3/4-lite"
  | "1/4-lite"
  | "vertical-door-lite"
  | "full-lite"
  | "3-lites"
  | "4-lites"
  | "centered-vertical-door-lite"
export type DoorGlass = "clear" | "frosted" | "decorative" | "energy-efficient"
export type DoorColor = "white" | "black" | "brown" | "beige" | "gray"
export type DoorHandle = "standard" | "premium" | "modern" | "classic"

export type ConfiguratorStep = "configuration" | "door" | "doorglass" | "colors" | "handle" | "summary"

interface DoorConfiguratorState {
  // Current step
  currentStep: ConfiguratorStep

  // Selections
  configuration: DoorConfiguration | null
  doorStyle: DoorStyle | null
  doorGlass: DoorGlass | null
  doorColor: DoorColor | null
  doorHandle: DoorHandle | null

  // Viewer state
  doorRotationY: number
  doorScale: number

  // Actions
  setCurrentStep: (step: ConfiguratorStep) => void
  setConfiguration: (config: DoorConfiguration) => void
  setDoorStyle: (style: DoorStyle) => void
  setDoorGlass: (glass: DoorGlass) => void
  setDoorColor: (color: DoorColor) => void
  setDoorHandle: (handle: DoorHandle) => void
  setDoorRotationY: (rotation: number) => void
  setDoorScale: (scale: number) => void
  reset: () => void
  canProceedToNext: () => boolean
  getNextStep: () => ConfiguratorStep | null
  getPreviousStep: () => ConfiguratorStep | null
}

const stepOrder: ConfiguratorStep[] = ["configuration", "door", "doorglass", "colors", "handle", "summary"]

export const useDoorConfiguratorStore = create<DoorConfiguratorState>()(
  persist(
    (set, get) => ({
      currentStep: "configuration",
      configuration: null,
      doorStyle: null,
      doorGlass: null,
      doorColor: null,
      doorHandle: null,
      doorRotationY: 0, // Initial rotation
      doorScale: 1, // Initial scale

      setCurrentStep: (step) => set({ currentStep: step }),

      setConfiguration: (configuration) => set({ configuration }),

      setDoorStyle: (doorStyle) => set({ doorStyle }),

      setDoorGlass: (doorGlass) => set({ doorGlass }),

      setDoorColor: (doorColor) => set({ doorColor }),

      setDoorHandle: (doorHandle) => set({ doorHandle }),

      setDoorRotationY: (rotation) => set({ doorRotationY: rotation }),
      setDoorScale: (scale) => set({ doorScale: scale }),

      reset: () =>
        set({
          currentStep: "configuration",
          configuration: null,
          doorStyle: null,
          doorGlass: null,
          doorColor: null,
          doorHandle: null,
          doorRotationY: 0, // Reset rotation
          doorScale: 1, // Reset scale
        }),

      canProceedToNext: () => {
        const state = get()
        switch (state.currentStep) {
          case "configuration":
            return state.configuration !== null
          case "door":
            return state.doorStyle !== null
          case "doorglass":
            return state.doorGlass !== null
          case "colors":
            return state.doorColor !== null
          case "handle":
            return state.doorHandle !== null
          case "summary":
            return true
          default:
            return false
        }
      },

      getNextStep: () => {
        const currentIndex = stepOrder.indexOf(get().currentStep)
        return currentIndex < stepOrder.length - 1 ? stepOrder[currentIndex + 1] : null
      },

      getPreviousStep: () => {
        const currentIndex = stepOrder.indexOf(get().currentStep)
        return currentIndex > 0 ? stepOrder[currentIndex - 1] : null
      },
    }),
    {
      name: "door-configurator-storage",
    },
  ),
)
