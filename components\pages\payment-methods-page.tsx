"use client"

import { Badge } from "@/components/ui/badge"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Plus, Trash2, CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface PaymentMethod {
  id: string
  type: "visa" | "mastercard" | "amex" | "discover"
  last4: string
  expiry: string
  isDefault: boolean
}

const getCardIcon = (type: PaymentMethod["type"]) => {
  switch (type) {
    case "visa":
      return "/placeholder.svg?height=24&width=38"
    case "mastercard":
      return "/placeholder.svg?height=24&width=38"
    case "amex":
      return "/placeholder.svg?height=24&width=38"
    case "discover":
      return "/placeholder.svg?height=24&width=38"
    default:
      return "/placeholder.svg?height=24&width=38"
  }
}

export default function PaymentMethodsPage() {
  const { toast } = useToast()
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    { id: "1", type: "visa", last4: "1234", expiry: "12/26", isDefault: true },
    { id: "2", type: "mastercard", last4: "5678", expiry: "08/25", isDefault: false },
  ])
  const [newCard, setNewCard] = useState({
    cardNumber: "",
    cardName: "",
    expiryMonth: "",
    expiryYear: "",
    cvv: "",
  })
  const [showAddCardForm, setShowAddCardForm] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setNewCard((prev) => ({ ...prev, [id]: value }))
  }

  const handleAddCard = (e: React.FormEvent) => {
    e.preventDefault()
    // Basic validation
    if (!newCard.cardNumber || !newCard.cardName || !newCard.expiryMonth || !newCard.expiryYear || !newCard.cvv) {
      toast({
        title: "Error",
        description: "Please fill in all card details.",
        variant: "destructive",
      })
      return
    }

    // Determine card type (simplified)
    let cardType: PaymentMethod["type"] = "visa"
    if (newCard.cardNumber.startsWith("4")) cardType = "visa"
    else if (newCard.cardNumber.startsWith("5")) cardType = "mastercard"
    else if (newCard.cardNumber.startsWith("34") || newCard.cardNumber.startsWith("37")) cardType = "amex"
    else if (newCard.cardNumber.startsWith("6")) cardType = "discover"

    const newPaymentMethod: PaymentMethod = {
      id: String(paymentMethods.length + 1),
      type: cardType,
      last4: newCard.cardNumber.slice(-4),
      expiry: `${newCard.expiryMonth}/${newCard.expiryYear.slice(-2)}`,
      isDefault: false, // New cards are not default by default
    }

    setPaymentMethods((prev) => [...prev, newPaymentMethod])
    setNewCard({ cardNumber: "", cardName: "", expiryMonth: "", expiryYear: "", cvv: "" })
    setShowAddCardForm(false)
    toast({
      title: "Success",
      description: "Payment method added successfully.",
    })
  }

  const handleDeleteCard = (id: string) => {
    setPaymentMethods((prev) => prev.filter((card) => card.id !== id))
    toast({
      title: "Payment Method Removed",
      description: "The selected payment method has been removed.",
    })
  }

  const handleSetDefault = (id: string) => {
    setPaymentMethods((prev) =>
      prev.map((card) => ({
        ...card,
        isDefault: card.id === id,
      })),
    )
    toast({
      title: "Default Payment Method Updated",
      description: "Your default payment method has been changed.",
    })
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="font-bebas text-3xl text-black">Payment Methods</h1>
        <p className="font-poppins text-gray-600">Manage your saved credit and debit cards.</p>
      </div>

      {/* Existing Payment Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Your Saved Cards</CardTitle>
          <CardDescription>Select your preferred payment method for faster checkout.</CardDescription>
        </CardHeader>
        <CardContent>
          {paymentMethods.length === 0 ? (
            <p className="text-gray-500">No payment methods saved yet.</p>
          ) : (
            <RadioGroup
              defaultValue={paymentMethods.find((p) => p.isDefault)?.id}
              onValueChange={handleSetDefault}
              className="space-y-4"
            >
              {paymentMethods.map((card) => (
                <div key={card.id} className="flex items-center justify-between rounded-md border p-4 hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <RadioGroupItem value={card.id} id={`card-${card.id}`} />
                    <img
                      src={getCardIcon(card.type) || "/placeholder.svg"}
                      alt={`${card.type} logo`}
                      className="h-6 w-auto"
                    />
                    <div>
                      <Label htmlFor={`card-${card.id}`} className="font-medium">
                        {card.type.toUpperCase()} ending in {card.last4}
                      </Label>
                      <p className="text-sm text-gray-500">Expires {card.expiry}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {card.isDefault && (
                      <Badge className="bg-green-500 text-white">
                        <CheckCircle className="mr-1 h-3 w-3" /> Default
                      </Badge>
                    )}
                    <Button variant="ghost" size="icon" onClick={() => handleDeleteCard(card.id)}>
                      <Trash2 className="h-4 w-4 text-red-500" />
                      <span className="sr-only">Delete card</span>
                    </Button>
                  </div>
                </div>
              ))}
            </RadioGroup>
          )}
        </CardContent>
      </Card>

      {/* Add New Payment Method */}
      <Card>
        <CardHeader>
          <CardTitle>Add New Payment Method</CardTitle>
          <CardDescription>Securely add a new credit or debit card to your account.</CardDescription>
        </CardHeader>
        <CardContent>
          {!showAddCardForm ? (
            <Button onClick={() => setShowAddCardForm(true)} className="w-full bg-primary-orange hover:bg-orange-600">
              <Plus className="mr-2 h-4 w-4" /> Add New Card
            </Button>
          ) : (
            <form onSubmit={handleAddCard} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input
                  id="cardNumber"
                  type="text"
                  placeholder="•••• •••• •••• ••••"
                  value={newCard.cardNumber}
                  onChange={handleInputChange}
                  maxLength={19} // Max length for card numbers with spaces
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cardName">Name on Card</Label>
                <Input
                  id="cardName"
                  type="text"
                  placeholder="John Doe"
                  value={newCard.cardName}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiryMonth">Expiry Month</Label>
                  <Input
                    id="expiryMonth"
                    type="text"
                    placeholder="MM"
                    value={newCard.expiryMonth}
                    onChange={handleInputChange}
                    maxLength={2}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="expiryYear">Expiry Year</Label>
                  <Input
                    id="expiryYear"
                    type="text"
                    placeholder="YYYY"
                    value={newCard.expiryYear}
                    onChange={handleInputChange}
                    maxLength={4}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    type="text"
                    placeholder="123"
                    value={newCard.cvv}
                    onChange={handleInputChange}
                    maxLength={4}
                    required
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button type="submit" className="bg-primary-orange hover:bg-orange-600">
                  Save Card
                </Button>
                <Button type="button" variant="outline" onClick={() => setShowAddCardForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
