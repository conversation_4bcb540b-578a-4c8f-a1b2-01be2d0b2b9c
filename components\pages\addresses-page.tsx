"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { MapPin, Plus, Edit, Trash2, Home, Building } from "lucide-react"
import Link from "next/link"

interface Address {
  id: string
  type: "home" | "business"
  name: string
  street: string
  city: string
  state: string
  zipCode: string
  country: string
  isDefault: boolean
}

export default function AddressesPage() {
  const [addresses, setAddresses] = useState<Address[]>([
    {
      id: "1",
      type: "home",
      name: "Home Address",
      street: "123 Main Street",
      city: "Anytown",
      state: "NY",
      zipCode: "12345",
      country: "United States",
      isDefault: true,
    },
    {
      id: "2",
      type: "business",
      name: "Office Address",
      street: "456 Business Ave",
      city: "Corporate City",
      state: "NY",
      zipCode: "54321",
      country: "United States",
      isDefault: false,
    },
  ])

  const [isAddingAddress, setIsAddingAddress] = useState(false)
  const [editingAddress, setEditingAddress] = useState<Address | null>(null)
  const [newAddress, setNewAddress] = useState<Partial<Address>>({
    type: "home",
    name: "",
    street: "",
    city: "",
    state: "",
    zipCode: "",
    country: "United States",
    isDefault: false,
  })

  const handleAddAddress = () => {
    if (newAddress.name && newAddress.street && newAddress.city && newAddress.state && newAddress.zipCode) {
      const address: Address = {
        id: Date.now().toString(),
        type: newAddress.type as "home" | "business",
        name: newAddress.name,
        street: newAddress.street,
        city: newAddress.city,
        state: newAddress.state,
        zipCode: newAddress.zipCode,
        country: newAddress.country || "United States",
        isDefault: newAddress.isDefault || false,
      }

      if (address.isDefault) {
        setAddresses((prev) => prev.map((addr) => ({ ...addr, isDefault: false })))
      }

      setAddresses((prev) => [...prev, address])
      setNewAddress({
        type: "home",
        name: "",
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "United States",
        isDefault: false,
      })
      setIsAddingAddress(false)
    }
  }

  const handleEditAddress = (address: Address) => {
    setEditingAddress(address)
    setNewAddress(address)
  }

  const handleUpdateAddress = () => {
    if (
      editingAddress &&
      newAddress.name &&
      newAddress.street &&
      newAddress.city &&
      newAddress.state &&
      newAddress.zipCode
    ) {
      const updatedAddress: Address = {
        ...editingAddress,
        type: newAddress.type as "home" | "business",
        name: newAddress.name,
        street: newAddress.street,
        city: newAddress.city,
        state: newAddress.state,
        zipCode: newAddress.zipCode,
        country: newAddress.country || "United States",
        isDefault: newAddress.isDefault || false,
      }

      if (updatedAddress.isDefault) {
        setAddresses((prev) => prev.map((addr) => ({ ...addr, isDefault: false })))
      }

      setAddresses((prev) => prev.map((addr) => (addr.id === editingAddress.id ? updatedAddress : addr)))
      setEditingAddress(null)
      setNewAddress({
        type: "home",
        name: "",
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "United States",
        isDefault: false,
      })
    }
  }

  const handleDeleteAddress = (id: string) => {
    setAddresses((prev) => prev.filter((addr) => addr.id !== id))
  }

  const handleSetDefault = (id: string) => {
    setAddresses((prev) => prev.map((addr) => ({ ...addr, isDefault: addr.id === id })))
  }

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
            <Link href="/profile" className="hover:text-primary-orange">
              Profile
            </Link>
            <span>/</span>
            <span>Manage Addresses</span>
          </div>
          <h1 className="font-bebas text-3xl text-gray-900">Manage Saved Addresses</h1>
          <p className="text-gray-600">Add, edit, or remove your saved addresses for faster checkout</p>
        </div>

        {/* Add New Address Button */}
        <div className="mb-6">
          <Dialog open={isAddingAddress} onOpenChange={setIsAddingAddress}>
            <DialogTrigger asChild>
              <Button className="bg-primary-orange hover:bg-orange-600 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add New Address
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add New Address</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Address Type</Label>
                  <Select
                    value={newAddress.type}
                    onValueChange={(value) =>
                      setNewAddress((prev) => ({ ...prev, type: value as "home" | "business" }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="home">Home</SelectItem>
                      <SelectItem value="business">Business</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Address Name</Label>
                  <Input
                    id="name"
                    value={newAddress.name}
                    onChange={(e) => setNewAddress((prev) => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Home, Office, etc."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="street">Street Address</Label>
                  <Input
                    id="street"
                    value={newAddress.street}
                    onChange={(e) => setNewAddress((prev) => ({ ...prev, street: e.target.value }))}
                    placeholder="123 Main Street"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={newAddress.city}
                      onChange={(e) => setNewAddress((prev) => ({ ...prev, city: e.target.value }))}
                      placeholder="City"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Input
                      id="state"
                      value={newAddress.state}
                      onChange={(e) => setNewAddress((prev) => ({ ...prev, state: e.target.value }))}
                      placeholder="NY"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="zipCode">ZIP Code</Label>
                  <Input
                    id="zipCode"
                    value={newAddress.zipCode}
                    onChange={(e) => setNewAddress((prev) => ({ ...prev, zipCode: e.target.value }))}
                    placeholder="12345"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={newAddress.isDefault}
                    onChange={(e) => setNewAddress((prev) => ({ ...prev, isDefault: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="isDefault">Set as default address</Label>
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleAddAddress} className="flex-1 bg-primary-orange hover:bg-orange-600">
                    Add Address
                  </Button>
                  <Button variant="outline" onClick={() => setIsAddingAddress(false)} className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Addresses List */}
        <div className="space-y-4">
          {addresses.map((address) => (
            <Card key={address.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                      {address.type === "home" ? (
                        <Home className="w-6 h-6 text-gray-600" />
                      ) : (
                        <Building className="w-6 h-6 text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-lg">{address.name}</h3>
                        {address.isDefault && <Badge className="bg-primary-orange text-white">Default</Badge>}
                        <Badge variant="outline" className="capitalize">
                          {address.type}
                        </Badge>
                      </div>
                      <div className="text-gray-600">
                        <p>{address.street}</p>
                        <p>
                          {address.city}, {address.state} {address.zipCode}
                        </p>
                        <p>{address.country}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {!address.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefault(address.id)}
                        className="text-xs"
                      >
                        Set Default
                      </Button>
                    )}
                    <Dialog
                      open={editingAddress?.id === address.id}
                      onOpenChange={(open) => !open && setEditingAddress(null)}
                    >
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm" onClick={() => handleEditAddress(address)}>
                          <Edit className="w-4 h-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>Edit Address</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="edit-type">Address Type</Label>
                            <Select
                              value={newAddress.type}
                              onValueChange={(value) =>
                                setNewAddress((prev) => ({ ...prev, type: value as "home" | "business" }))
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="home">Home</SelectItem>
                                <SelectItem value="business">Business</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="edit-name">Address Name</Label>
                            <Input
                              id="edit-name"
                              value={newAddress.name}
                              onChange={(e) => setNewAddress((prev) => ({ ...prev, name: e.target.value }))}
                              placeholder="e.g., Home, Office, etc."
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="edit-street">Street Address</Label>
                            <Input
                              id="edit-street"
                              value={newAddress.street}
                              onChange={(e) => setNewAddress((prev) => ({ ...prev, street: e.target.value }))}
                              placeholder="123 Main Street"
                            />
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="edit-city">City</Label>
                              <Input
                                id="edit-city"
                                value={newAddress.city}
                                onChange={(e) => setNewAddress((prev) => ({ ...prev, city: e.target.value }))}
                                placeholder="City"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="edit-state">State</Label>
                              <Input
                                id="edit-state"
                                value={newAddress.state}
                                onChange={(e) => setNewAddress((prev) => ({ ...prev, state: e.target.value }))}
                                placeholder="NY"
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="edit-zipCode">ZIP Code</Label>
                            <Input
                              id="edit-zipCode"
                              value={newAddress.zipCode}
                              onChange={(e) => setNewAddress((prev) => ({ ...prev, zipCode: e.target.value }))}
                              placeholder="12345"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="edit-isDefault"
                              checked={newAddress.isDefault}
                              onChange={(e) => setNewAddress((prev) => ({ ...prev, isDefault: e.target.checked }))}
                              className="rounded"
                            />
                            <Label htmlFor="edit-isDefault">Set as default address</Label>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              onClick={handleUpdateAddress}
                              className="flex-1 bg-primary-orange hover:bg-orange-600"
                            >
                              Update Address
                            </Button>
                            <Button variant="outline" onClick={() => setEditingAddress(null)} className="flex-1">
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteAddress(address.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {addresses.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No addresses saved</h3>
              <p className="text-gray-600 mb-6">Add your first address to make checkout faster and easier.</p>
              <Button
                onClick={() => setIsAddingAddress(true)}
                className="bg-primary-orange hover:bg-orange-600 text-white"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Address
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </main>
  )
}
