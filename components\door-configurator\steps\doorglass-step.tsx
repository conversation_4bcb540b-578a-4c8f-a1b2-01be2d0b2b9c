"use client"

import { motion } from "framer-motion"
import { useDoorConfiguratorStore, type DoorGlass } from "@/lib/door-configurator-store"
import { Search } from "lucide-react"

const glassOptions: { key: DoorGlass; label: string; description: string }[] = [
  { key: "clear", label: "Clear Glass", description: "Standard clear glass" },
  { key: "frosted", label: "Frosted Glass", description: "Privacy frosted glass" },
  { key: "decorative", label: "Decorative Glass", description: "Decorative patterns" },
  { key: "energy-efficient", label: "Energy Efficient", description: "Low-E coated glass" },
]

export function DoorglassStep() {
  const { doorGlass, setDoorGlass } = useDoorConfiguratorStore()

  const renderGlassPreview = (glass: DoorGlass) => {
    const baseClasses = "w-full h-24 rounded border-2 border-gray-300"

    switch (glass) {
      case "clear":
        return <div className={`${baseClasses} bg-blue-50 bg-opacity-30`} />
      case "frosted":
        return <div className={`${baseClasses} bg-white bg-opacity-70`} />
      case "decorative":
        return <div className={`${baseClasses} bg-gradient-to-br from-blue-100 to-purple-100`} />
      case "energy-efficient":
        return <div className={`${baseClasses} bg-green-50 bg-opacity-50`} />
      default:
        return <div className={`${baseClasses} bg-gray-100`} />
    }
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">DOOR GLASS</h2>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {glassOptions.map((glass) => (
          <motion.button
            key={glass.key}
            onClick={() => setDoorGlass(glass.key)}
            className={`
              p-4 rounded-lg border-2 text-left transition-all duration-200
              ${
                doorGlass === glass.key
                  ? "border-orange-500 bg-orange-50"
                  : "border-gray-200 bg-white hover:border-gray-300"
              }
            `}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {renderGlassPreview(glass.key)}
            <h3 className="font-medium text-gray-900 mt-3">{glass.label}</h3>
            <p className="text-sm text-gray-600 mt-1">{glass.description}</p>
          </motion.button>
        ))}
      </div>
    </div>
  )
}
