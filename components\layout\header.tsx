"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Search, ShoppingCart, Star, Phone, User, Menu, X } from "lucide-react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import SearchBarOverlay from "@/components/ui/search-bar-overlay"
import CartPreviewOverlay from "@/components/ui/cart-preview-overlay"
import { useCartStore } from "@/lib/cart-store"
import { isAuthenticated, logout, getCurrentUser } from "@/lib/auth"

export default function Header() {
  const pathname = usePathname()
  const router = useRouter()
  const [showSearchBar, setShowSearchBar] = useState(false)
  const [isAuth, setIsAuth] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const { isOpen: showCartPreview, setIsOpen: setShowCartPreview, getTotalItems } = useCartStore()
  const cartItemCount = getTotalItems()

  useEffect(() => {
    setIsAuth(isAuthenticated())
    setUser(getCurrentUser())
  }, [])

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(path)
  }

  const handleSignOut = () => {
    logout()
    setIsAuth(false)
    setUser(null)
    router.push("/")
  }

  const navigationItems = [
    { href: "/about", label: "ABOUT US" },
    { href: "/instant-quote", label: "VINYL WINDOWS" },
    { href: "/products/insulated-glass-units", label: "SEALED GLASS UNITS" },
    { href: "/products/patio-doors", label: "PATIO DOORS" },
    { href: "/options", label: "OPTIONS" },
    { href: "/contact", label: "CONTACT" },
    { href: "/faq", label: "FAQ" },
  ]

  return (
    <header className="bg-white sticky top-0 z-50 shadow-sm">
      {/* Top black bar */}
      <div className="bg-black py-2 px-4">
        <div className="max-w-7xl mx-auto flex flex-col sm:flex-row justify-end items-center space-y-2 sm:space-y-0 sm:space-x-6 text-center sm:text-right">
          <Link href="#" className="flex items-center gap-2 text-white hover:text-primary-orange transition-colors">
            <Star className="w-4 h-4 text-primary-orange" />
            <span className="text-sm font-poppins">Reviews</span>
          </Link>
          {isAuth && (
            <Link
              href="/profile/quotes"
              className="flex items-center gap-2 text-white hover:text-primary-orange transition-colors"
            >
              <User className="w-4 h-4 text-primary-orange" />
              <span className="text-sm font-poppins">My Quotes</span>
            </Link>
          )}
          <Link
            href="/appointments"
            className="flex items-center gap-2 text-white hover:text-primary-orange transition-colors"
          >
            <Phone className="w-4 h-4 text-primary-orange" />
            <span className="text-sm font-poppins">Book Appointment</span>
          </Link>
          <Link
            href="tel:(*************"
            className="flex items-center gap-2 text-white hover:text-primary-orange transition-colors"
          >
            <Phone className="w-4 h-4 text-primary-orange" />
            <span className="text-sm font-poppins">(*************</span>
          </Link>
          {isAuth ? (
            <div className="flex items-center gap-4">
              <Link href="/profile" className="text-white hover:text-primary-orange text-sm">
                Welcome, {user?.name}
              </Link>
              <button
                onClick={handleSignOut}
                className="flex items-center gap-2 text-white hover:text-primary-orange transition-colors"
              >
                <User className="w-4 h-4 text-primary-orange" />
                <span className="text-sm font-poppins">Sign Out</span>
              </button>
            </div>
          ) : (
            <Link
              href="/sign-in"
              className="flex items-center gap-2 text-white hover:text-primary-orange transition-colors"
            >
              <User className="w-4 h-4 text-primary-orange" />
              <span className="text-sm font-poppins">Sign In / Create Account</span>
            </Link>
          )}
        </div>
      </div>

      {/* Main header with centered logo */}
      <div className="bg-white py-6">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between">
            {/* Left spacer for mobile */}
            <div className="w-6 md:w-0"></div>

            {/* Centered RAF Logo */}
            <Link href="/" className="flex items-center justify-center flex-1 md:flex-none">
              <div className="text-center">
                <span className="block text-6xl font-bebas font-bold text-black">RAF</span>
              </div>
            </Link>

            {/* Desktop Icons */}
            <div className="hidden md:flex items-center space-x-4">
              <Search
                className="w-6 h-6 text-black cursor-pointer hover:text-primary-orange transition-colors"
                onClick={() => setShowSearchBar(true)}
                aria-label="Open search bar"
              />
              <div className="relative">
                <ShoppingCart
                  className="w-6 h-6 text-black cursor-pointer hover:text-primary-orange transition-colors"
                  onClick={() => setShowCartPreview(true)}
                  aria-label="Open cart preview"
                />
                {cartItemCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-primary-orange text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-poppins animate-pulse">
                    {cartItemCount > 99 ? "99+" : cartItemCount}
                  </span>
                )}
              </div>
            </div>

            {/* Mobile menu button */}
            <button
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? <X className="w-6 h-6 text-black" /> : <Menu className="w-6 h-6 text-black" />}
            </button>
          </div>
        </div>
      </div>

      {/* Navigation bar */}
      <div className="bg-primary-orange">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="hidden md:flex items-center justify-between">
            <nav className="flex flex-wrap justify-center gap-x-8 gap-y-2">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`text-white font-poppins font-medium text-sm hover:text-black transition-colors ${
                    isActive(item.href) ? "text-black" : ""
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </nav>
            <Link href="/instant-quote">
              <Button
                className={`bg-white text-primary-orange hover:bg-gray-100 px-6 py-2 font-poppins font-semibold text-sm transition-colors ${
                  isActive("/instant-quote") ? "bg-gray-100" : ""
                }`}
              >
                INSTANT QUOTE
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t shadow-lg">
          <div className="px-4 py-4 space-y-4">
            {/* Mobile search and cart */}
            <div className="flex items-center justify-center space-x-6 pb-4 border-b">
              <Search
                className="w-6 h-6 text-black cursor-pointer hover:text-primary-orange transition-colors"
                onClick={() => {
                  setShowSearchBar(true)
                  setIsMobileMenuOpen(false)
                }}
                aria-label="Open search bar"
              />
              <div className="relative">
                <ShoppingCart
                  className="w-6 h-6 text-black cursor-pointer hover:text-primary-orange transition-colors"
                  onClick={() => {
                    setShowCartPreview(true)
                    setIsMobileMenuOpen(false)
                  }}
                  aria-label="Open cart preview"
                />
                {cartItemCount > 99
                  ? "99+"
                  : cartItemCount > 0 && (
                      <span className="absolute -top-2 -right-2 bg-primary-orange text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-poppins">
                        {cartItemCount}
                      </span>
                    )}
              </div>
            </div>

            {/* Mobile navigation */}
            <nav className="space-y-3">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block text-gray-900 font-poppins font-medium text-sm hover:text-primary-orange transition-colors py-2 ${
                    isActive(item.href) ? "text-primary-orange" : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </nav>

            {/* Mobile CTA */}
            <div className="pt-4 border-t">
              <Link href="/instant-quote" onClick={() => setIsMobileMenuOpen(false)}>
                <Button className="w-full bg-primary-orange hover:bg-orange-600 text-white font-poppins font-semibold">
                  INSTANT QUOTE
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Search Bar Overlay */}
      <SearchBarOverlay isOpen={showSearchBar} onClose={() => setShowSearchBar(false)} />

      {/* Cart Preview Overlay */}
      <CartPreviewOverlay isOpen={showCartPreview} onClose={() => setShowCartPreview(false)} />
    </header>
  )
}
