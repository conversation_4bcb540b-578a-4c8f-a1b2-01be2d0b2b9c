"use client"

import { useDoorConfiguratorStore } from "@/lib/door-configurator-store"
import { But<PERSON> } from "@/components/ui/button"
import { Check, Edit } from "lucide-react"

export function SummaryStep() {
  const { configuration, doorStyle, doorGlass, doorColor, doorHandle, setCurrentStep } = useDoorConfiguratorStore()

  const calculatePrice = () => {
    let basePrice = 850 // Base door price

    // Configuration pricing
    const configPricing = {
      "single-door": 0,
      "double-doors": 400,
      "single-door-right-sidelite": 200,
      "single-door-left-sidelite": 200,
      "single-door-two-sidelites": 350,
      "single-door-transom": 150,
      "single-door-left-sidelite-and-transom": 300,
      "single-door-right-sidelite-and-transom": 300,
      "single-door-two-sidelites-and-transom": 450,
      "double-doors-right-sidelite": 550,
      "double-doors-left-sidelite": 550,
      "double-doors-two-sidelites": 700,
      "double-doors-transom": 500,
      "single-door-right-extra-large-sidelite": 280,
      "single-door-left-extra-large-sidelite": 280,
      "single-door-left-extra-large-sidelite-and-transom": 380,
      "single-door-right-extra-large-sidelite-and-transom": 380,
    }

    // Door style pricing
    const stylePricing = {
      "without-door-lite": 0,
      "1/2-lite": 50,
      "3/4-lite": 75,
      "1/4-lite": 40,
      "vertical-door-lite": 60,
      "full-lite": 100,
      "3-lites": 80,
      "4-lites": 90,
      "centered-vertical-door-lite": 65,
    }

    // Glass pricing
    const glassPricing = {
      clear: 0,
      frosted: 30,
      decorative: 80,
      "energy-efficient": 120,
    }

    // Handle pricing
    const handlePricing = {
      standard: 0,
      premium: 45,
      modern: 35,
      classic: 25,
    }

    if (configuration) basePrice += configPricing[configuration] || 0
    if (doorStyle) basePrice += stylePricing[doorStyle] || 0
    if (doorGlass) basePrice += glassPricing[doorGlass] || 0
    if (doorHandle) basePrice += handlePricing[doorHandle] || 0

    return basePrice
  }

  const formatLabel = (value: string) => {
    return value.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
  }

  const summaryItems = [
    { label: "Configuration", value: configuration, step: "configuration" },
    { label: "Door Style", value: doorStyle, step: "door" },
    { label: "Glass Type", value: doorGlass, step: "doorglass" },
    { label: "Door Color", value: doorColor, step: "colors" },
    { label: "Handle Style", value: doorHandle, step: "handle" },
  ]

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bebas text-gray-800 mb-6">Configuration Summary</h2>

      <div className="space-y-4 mb-8">
        {summaryItems.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
            <div className="flex items-center space-x-3">
              <Check className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-poppins font-medium text-gray-800">{item.label}</p>
                <p className="font-poppins text-sm text-gray-600">
                  {item.value ? formatLabel(item.value) : "Not selected"}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentStep(item.step as any)}
              className="text-primary-orange hover:text-orange-700"
            >
              <Edit className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>

      <div className="bg-primary-orange text-white p-6 rounded-lg">
        <div className="flex justify-between items-center">
          <span className="font-bebas text-xl">Total Price:</span>
          <span className="font-bebas text-3xl">${calculatePrice().toFixed(2)}</span>
        </div>
        <p className="font-poppins text-sm mt-2 opacity-90">*Price includes all selected options and configurations</p>
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-poppins font-semibold text-blue-800 mb-2">What's Next?</h3>
        <ul className="font-poppins text-sm text-blue-700 space-y-1">
          <li>• Add to cart to proceed with purchase</li>
          <li>• Download quote for your records</li>
          <li>• Share configuration with others</li>
          <li>• Contact us for installation scheduling</li>
        </ul>
      </div>
    </div>
  )
}
