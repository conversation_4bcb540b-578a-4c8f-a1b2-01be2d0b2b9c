"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import StarRating from "@/components/ui/star-rating"
import ReviewForm from "@/components/ui/review-form"
import { MessageSquare, ThumbsUp, User } from "lucide-react"

interface Review {
  id: string
  rating: number
  title: string
  comment: string
  name: string
  date: string
  helpful: number
  verified: boolean
}

interface ProductReviewsProps {
  productName: string
  reviews?: Review[]
}

export default function ProductReviews({ productName, reviews = [] }: ProductReviewsProps) {
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [currentReviews, setCurrentReviews] = useState<Review[]>(
    reviews.length > 0
      ? reviews
      : [
          {
            id: "1",
            rating: 5,
            title: "Excellent Quality Windows",
            comment:
              "These windows exceeded my expectations. The installation was smooth and the energy efficiency is noticeable. Highly recommend!",
            name: "<PERSON>",
            date: "2024-01-15",
            helpful: 12,
            verified: true,
          },
          {
            id: "2",
            rating: 4,
            title: "Great Value for Money",
            comment:
              "Good quality windows at a reasonable price. The customer service was helpful throughout the process.",
            name: "<PERSON>",
            date: "2024-01-10",
            helpful: 8,
            verified: true,
          },
          {
            id: "3",
            rating: 5,
            title: "Professional Installation",
            comment: "The installation team was professional and efficient. The windows look great and work perfectly.",
            name: "Jennifer L.",
            date: "2024-01-05",
            helpful: 15,
            verified: false,
          },
        ],
  )

  const averageRating =
    currentReviews.length > 0
      ? currentReviews.reduce((sum, review) => sum + review.rating, 0) / currentReviews.length
      : 0

  const ratingDistribution = [5, 4, 3, 2, 1].map((rating) => ({
    rating,
    count: currentReviews.filter((review) => review.rating === rating).length,
    percentage:
      currentReviews.length > 0
        ? (currentReviews.filter((review) => review.rating === rating).length / currentReviews.length) * 100
        : 0,
  }))

  const handleSubmitReview = async (reviewData: {
    rating: number
    title: string
    comment: string
    name: string
    email: string
  }) => {
    const newReview: Review = {
      id: Date.now().toString(),
      rating: reviewData.rating,
      title: reviewData.title,
      comment: reviewData.comment,
      name: reviewData.name,
      date: new Date().toISOString().split("T")[0],
      helpful: 0,
      verified: false,
    }

    setCurrentReviews((prev) => [newReview, ...prev])
  }

  const handleHelpful = (reviewId: string) => {
    setCurrentReviews((prev) =>
      prev.map((review) => (review.id === reviewId ? { ...review, helpful: review.helpful + 1 } : review)),
    )
  }

  return (
    <div className="bg-white py-12">
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-8">
          <h2 className="text-3xl font-bebas text-black mb-6">Customer Reviews</h2>

          {/* Rating Summary */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex items-center space-x-4 mb-4">
                <div className="text-4xl font-bold text-black">{averageRating.toFixed(1)}</div>
                <div>
                  <StarRating rating={Math.round(averageRating)} readonly size="lg" />
                  <p className="text-gray-600 text-sm mt-1">Based on {currentReviews.length} reviews</p>
                </div>
              </div>

              <div className="space-y-2">
                {ratingDistribution.map(({ rating, count, percentage }) => (
                  <div key={rating} className="flex items-center space-x-3">
                    <span className="text-sm w-8">{rating}★</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-8">{count}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex flex-col justify-center">
              <h3 className="text-xl font-bebas text-black mb-4">Share Your Experience</h3>
              <p className="text-gray-600 mb-4">Help other customers by sharing your experience with this product.</p>
              <Button
                onClick={() => setShowReviewForm(true)}
                className="bg-primary-orange hover:bg-orange-700 text-white w-fit"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Write a Review
              </Button>
            </div>
          </div>
        </div>

        {/* Reviews List */}
        <div className="space-y-6">
          {currentReviews.map((review) => (
            <div key={review.id} className="border-b border-gray-200 pb-6">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3">
                <div className="flex items-center space-x-3 mb-2 sm:mb-0">
                  <div className="flex items-center space-x-2">
                    <User className="w-5 h-5 text-gray-400" />
                    <span className="font-medium text-black">{review.name}</span>
                    {review.verified && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Verified Purchase</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <StarRating rating={review.rating} readonly size="sm" />
                  <span className="text-gray-500 text-sm">{review.date}</span>
                </div>
              </div>

              <h4 className="font-semibold text-black mb-2">{review.title}</h4>
              <p className="text-gray-700 mb-4 leading-relaxed">{review.comment}</p>

              <div className="flex items-center justify-between">
                <button
                  onClick={() => handleHelpful(review.id)}
                  className="flex items-center space-x-2 text-gray-500 hover:text-primary-orange transition-colors"
                >
                  <ThumbsUp className="w-4 h-4" />
                  <span className="text-sm">Helpful ({review.helpful})</span>
                </button>
              </div>
            </div>
          ))}
        </div>

        {currentReviews.length === 0 && (
          <div className="text-center py-12">
            <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-bebas text-gray-500 mb-2">No Reviews Yet</h3>
            <p className="text-gray-500 mb-4">Be the first to review this product!</p>
            <Button
              onClick={() => setShowReviewForm(true)}
              className="bg-primary-orange hover:bg-orange-700 text-white"
            >
              Write the First Review
            </Button>
          </div>
        )}

        {/* Review Form Modal */}
        {showReviewForm && (
          <ReviewForm
            productName={productName}
            onSubmit={handleSubmitReview}
            onClose={() => setShowReviewForm(false)}
          />
        )}
      </div>
    </div>
  )
}
