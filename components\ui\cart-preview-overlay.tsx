"use client"

import { useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { X, ShoppingCart, Plus, Minus, Trash2 } from "lucide-react"
import Link from "next/link"
import { useCartStore } from "@/lib/cart-store"
import Image from "next/image"

interface CartPreviewOverlayProps {
  isOpen: boolean
  onClose: () => void
}

export default function CartPreviewOverlay({ isOpen, onClose }: CartPreviewOverlayProps) {
  const sidebarRef = useRef<HTMLDivElement>(null)
  const { items, updateQuantity, removeItem, getTotalPrice, getTotalItems } = useCartStore()

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscape)
    } else {
      document.removeEventListener("keydown", handleEscape)
    }

    return () => {
      document.removeEventListener("keydown", handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  const totalItems = getTotalItems()
  const totalPrice = getTotalPrice()

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[100] flex justify-end transition-opacity duration-300 ease-in-out"
      onClick={(e) => {
        if (sidebarRef.current && !sidebarRef.current.contains(e.target as Node)) {
          onClose()
        }
      }}
    >
      <div
        ref={sidebarRef}
        className="bg-white w-full max-w-md h-full shadow-lg transform transition-transform duration-300 ease-in-out animate-slide-in-right flex flex-col"
      >
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="font-bebas text-2xl text-black flex items-center gap-2">
            <ShoppingCart className="w-6 h-6 text-primary-orange" />
            Your Cart ({totalItems})
          </h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-800"
            aria-label="Close cart preview"
          >
            <X className="w-6 h-6" />
          </Button>
        </div>

        {/* Cart Items */}
        <div className="flex-1 overflow-y-auto p-6">
          {items.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-48 text-gray-600 font-poppins">
              <ShoppingCart className="w-16 h-16 text-gray-300 mb-4" />
              <p className="text-lg mb-2">Your cart is empty.</p>
              <p className="text-sm">Start shopping to add items!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {items.map((item) => (
                <div key={item.id} className="border-b pb-4 last:border-b-0">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={item.image || "/placeholder.svg"}
                        alt={item.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="font-poppins font-medium text-gray-800 text-sm truncate">{item.name}</h3>

                      {/* Specifications */}
                      {item.specifications && (
                        <div className="text-xs text-gray-500 mt-1">
                          {item.specifications.width}" × {item.specifications.height}" • {item.specifications.coating}
                        </div>
                      )}

                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-6 h-6 p-0 bg-transparent"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="w-3 h-3" />
                          </Button>
                          <span className="font-poppins text-sm font-medium w-8 text-center">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-6 h-6 p-0 bg-transparent"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className="font-poppins text-sm font-semibold text-primary-orange">
                            ${(item.price * item.quantity).toFixed(2)}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-6 h-6 p-0 text-red-500 hover:text-red-700"
                            onClick={() => removeItem(item.id)}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {items.length > 0 && (
          <div className="border-t p-6 bg-gray-50">
            <div className="flex justify-between items-center mb-4">
              <span className="font-bebas text-xl text-black">Subtotal:</span>
              <span className="font-bebas text-xl text-primary-orange">${totalPrice.toFixed(2)}</span>
            </div>
            <Link href="/checkout" onClick={onClose}>
              <Button className="w-full bg-primary-orange hover:bg-orange-700 text-white py-3 font-poppins font-semibold transition-colors mb-2">
                Proceed to Checkout
              </Button>
            </Link>
            <Button
              variant="outline"
              onClick={onClose}
              className="w-full border-gray-300 text-gray-700 hover:bg-gray-100 font-poppins font-semibold transition-colors bg-transparent"
            >
              Continue Shopping
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
