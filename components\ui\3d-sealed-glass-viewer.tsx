"use client"

import { Suspense, useRef, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { OrbitControls, Center, Environment, Html } from "@react-three/drei"
import { Button } from "@/components/ui/button"
import { RotateCcw, Eye, EyeOff } from "lucide-react"
import * as THREE from "three"

interface SealedGlassModelProps {
  width: number
  height: number
  thickness: number
  coating: string
  spacer: string
  gasFill: string
  grid: boolean
  wireframeMode?: boolean
}

function SealedGlassModel({
  width,
  height,
  thickness,
  coating,
  spacer,
  gasFill,
  grid,
  wireframeMode = false,
}: SealedGlassModelProps) {
  const groupRef = useRef<THREE.Group>(null)

  // Convert dimensions to 3D scale (normalize to reasonable size)
  const scaleX = Math.max(width / 100, 0.5)
  const scaleY = Math.max(height / 100, 0.5)
  const scaleZ = Math.max(thickness / 50, 0.1)

  // Glass material based on coating
  const getGlassMaterial = () => {
    const baseProps = {
      transparent: true,
      opacity: 0.3,
      roughness: 0.1,
      metalness: 0.1,
      wireframe: wireframeMode,
    }

    switch (coating) {
      case "low-e":
        return new THREE.MeshPhysicalMaterial({
          ...baseProps,
          color: new THREE.Color(0.9, 0.95, 1.0),
          transmission: 0.9,
          thickness: 0.1,
        })
      case "reflective":
        return new THREE.MeshPhysicalMaterial({
          ...baseProps,
          color: new THREE.Color(0.8, 0.9, 1.0),
          metalness: 0.3,
          transmission: 0.7,
        })
      case "tinted":
        return new THREE.MeshPhysicalMaterial({
          ...baseProps,
          color: new THREE.Color(0.7, 0.8, 0.6),
          transmission: 0.8,
        })
      default:
        return new THREE.MeshPhysicalMaterial({
          ...baseProps,
          color: new THREE.Color(0.95, 0.95, 1.0),
          transmission: 0.95,
        })
    }
  }

  // Spacer material based on type
  const getSpacerMaterial = () => {
    const color = spacer === "warm-edge" ? 0x8b4513 : 0x708090
    return new THREE.MeshStandardMaterial({
      color,
      wireframe: wireframeMode,
    })
  }

  return (
    <group ref={groupRef} scale={[scaleX, scaleY, scaleZ]}>
      {/* Outer glass pane */}
      <mesh position={[0, 0, 0.5]}>
        <boxGeometry args={[2, 2, 0.1]} />
        <primitive object={getGlassMaterial()} />
      </mesh>

      {/* Inner glass pane */}
      <mesh position={[0, 0, -0.5]}>
        <boxGeometry args={[2, 2, 0.1]} />
        <primitive object={getGlassMaterial()} />
      </mesh>

      {/* Spacer frame */}
      <group>
        {/* Top spacer */}
        <mesh position={[0, 0.9, 0]}>
          <boxGeometry args={[2, 0.2, 1]} />
          <primitive object={getSpacerMaterial()} />
        </mesh>
        {/* Bottom spacer */}
        <mesh position={[0, -0.9, 0]}>
          <boxGeometry args={[2, 0.2, 1]} />
          <primitive object={getSpacerMaterial()} />
        </mesh>
        {/* Left spacer */}
        <mesh position={[-0.9, 0, 0]}>
          <boxGeometry args={[0.2, 1.8, 1]} />
          <primitive object={getSpacerMaterial()} />
        </mesh>
        {/* Right spacer */}
        <mesh position={[0.9, 0, 0]}>
          <boxGeometry args={[0.2, 1.8, 1]} />
          <primitive object={getSpacerMaterial()} />
        </mesh>
      </group>

      {/* Gas fill visualization (subtle tint in the cavity) */}
      {gasFill !== "air" && (
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[1.8, 1.8, 0.8]} />
          <meshBasicMaterial
            color={gasFill === "argon" ? 0x87ceeb : 0x98fb98}
            transparent
            opacity={0.1}
            wireframe={wireframeMode}
          />
        </mesh>
      )}

      {/* Grid pattern if enabled */}
      {grid && (
        <group position={[0, 0, 0.51]}>
          {/* Vertical grid lines */}
          {[-0.5, 0, 0.5].map((x, i) => (
            <mesh key={`v-${i}`} position={[x, 0, 0]}>
              <boxGeometry args={[0.02, 2, 0.01]} />
              <meshStandardMaterial color={0x333333} wireframe={wireframeMode} />
            </mesh>
          ))}
          {/* Horizontal grid lines */}
          {[-0.5, 0, 0.5].map((y, i) => (
            <mesh key={`h-${i}`} position={[0, y, 0]}>
              <boxGeometry args={[2, 0.02, 0.01]} />
              <meshStandardMaterial color={0x333333} wireframe={wireframeMode} />
            </mesh>
          ))}
        </group>
      )}
    </group>
  )
}

function Loader() {
  return (
    <Html center>
      <div className="flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-orange"></div>
      </div>
    </Html>
  )
}

interface SealedGlass3DViewerProps {
  width: number
  height: number
  thickness: number
  coating: string
  spacer: string
  gasFill: string
  grid: boolean
}

export default function SealedGlass3DViewer({
  width,
  height,
  thickness,
  coating,
  spacer,
  gasFill,
  grid,
}: SealedGlass3DViewerProps) {
  const controlsRef = useRef<any>(null)
  const modelRef = useRef<THREE.Group>(null)
  const [wireframeMode, setWireframeMode] = useState(false)

  const resetView = () => {
    if (controlsRef.current) {
      controlsRef.current.reset()
    }
  }

  const toggleWireframe = () => {
    setWireframeMode(!wireframeMode)
  }

  return (
    <div className="relative w-full h-full">
      {/* Controls */}
      <div className="absolute top-4 right-4 z-10 flex gap-2">
        <Button variant="outline" size="sm" onClick={resetView} className="bg-white/90 backdrop-blur-sm hover:bg-white">
          <RotateCcw className="w-4 h-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={toggleWireframe}
          className="bg-white/90 backdrop-blur-sm hover:bg-white"
        >
          {wireframeMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </Button>
      </div>

      {/* Specifications Display */}
      <div className="absolute top-4 left-4 z-10 bg-white/90 backdrop-blur-sm rounded-lg p-3 text-xs">
        <div className="space-y-1">
          <div>
            <strong>Size:</strong> {width}" × {height}"
          </div>
          <div>
            <strong>Thickness:</strong> {thickness}mm
          </div>
          <div>
            <strong>Coating:</strong> {coating}
          </div>
          <div>
            <strong>Spacer:</strong> {spacer}
          </div>
          <div>
            <strong>Gas:</strong> {gasFill}
          </div>
        </div>
      </div>

      <Canvas
        camera={{
          position: [2, 2, 5],
          fov: 45,
          near: 0.1,
          far: 100,
        }}
        style={{ width: "100%", height: "100%" }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: "high-performance",
        }}
        shadows
      >
        <Suspense fallback={<Loader />}>
          {/* Enhanced lighting for glass visualization */}
          <ambientLight intensity={0.6} color="#ffffff" />
          <directionalLight
            position={[10, 10, 5]}
            intensity={2}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            color="#ffffff"
          />
          <directionalLight position={[-8, 8, 8]} intensity={1.2} color="#e6f3ff" />
          <pointLight position={[0, 6, 0]} intensity={0.8} color="#ffffff" />
          <spotLight position={[6, 6, 6]} angle={0.3} penumbra={0.2} intensity={1.5} castShadow color="#ffffff" />

          <Center>
            <SealedGlassModel
              ref={modelRef}
              width={width}
              height={height}
              thickness={thickness}
              coating={coating}
              spacer={spacer}
              gasFill={gasFill}
              grid={grid}
              wireframeMode={wireframeMode}
            />
          </Center>

          {/* Studio environment for better glass rendering */}
          <Environment preset="studio" background={false} blur={0.1} />

          <OrbitControls
            ref={controlsRef}
            enablePan={true}
            enableRotate={true}
            enableZoom={true}
            enableDamping={true}
            dampingFactor={0.05}
            minDistance={2}
            maxDistance={10}
            maxPolarAngle={Math.PI / 1.4}
            minPolarAngle={Math.PI / 6}
            autoRotate={false}
            target={[0, 0, 0]}
          />
        </Suspense>
      </Canvas>
    </div>
  )
}
