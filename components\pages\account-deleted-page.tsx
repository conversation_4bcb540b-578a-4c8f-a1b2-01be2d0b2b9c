"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { CheckCircle } from "lucide-react"

export default function AccountDeletedPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <CardTitle className="mt-4 text-3xl font-bebas text-green-700">Account Deleted</CardTitle>
          <CardDescription className="font-poppins text-gray-600">
            Your account has been successfully deleted. We&apos;re sorry to see you go.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-500">If you change your mind, you can always create a new account.</p>
          <Link href="/">
            <Button className="w-full bg-primary-orange hover:bg-orange-600">Return to Homepage</Button>
          </Link>
          <Link href="/sign-in">
            <Button variant="outline" className="w-full bg-transparent">
              Sign In
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  )
}
