"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import SealedGlass3DViewer from "@/components/ui/3d-sealed-glass-viewer"
import AddToCartButton from "@/components/ui/add-to-cart-button"
import { ShoppingCart, Info, Star, Award, Shield, Zap } from "lucide-react"

export default function InsulatedGlassUnitsPage() {
  const [selectedSpecs, setSelectedSpecs] = useState({
    width: 24,
    height: 36,
    thickness: 16,
    coating: "clear",
    spacer: "aluminum",
    gasFill: "air",
    grid: false,
  })

  const [quantity, setQuantity] = useState(1)

  // Calculate price based on specifications
  const calculatePrice = () => {
    let basePrice = 45

    // Size multiplier
    const area = (selectedSpecs.width * selectedSpecs.height) / 144 // sq ft
    basePrice *= area

    // Coating premium
    const coatingPremiums = {
      clear: 0,
      "low-e": 15,
      reflective: 25,
      tinted: 10,
    }
    basePrice += coatingPremiums[selectedSpecs.coating as keyof typeof coatingPremiums] || 0

    // Spacer premium
    if (selectedSpecs.spacer === "warm-edge") basePrice += 8

    // Gas fill premium
    const gasPremiums = {
      air: 0,
      argon: 12,
      krypton: 35,
    }
    basePrice += gasPremiums[selectedSpecs.gasFill as keyof typeof gasPremiums] || 0

    // Grid premium
    if (selectedSpecs.grid) basePrice += 20

    // Thickness premium
    if (selectedSpecs.thickness > 20) basePrice += 10

    return Math.round(basePrice * 100) / 100
  }

  const unitPrice = calculatePrice()
  const totalPrice = unitPrice * quantity

  const productData = {
    id: "sealed-glass-unit",
    name: `Custom Sealed Glass Unit - ${selectedSpecs.width}" × ${selectedSpecs.height}"`,
    price: unitPrice,
    image: "/placeholder.svg?height=300&width=300&text=Sealed+Glass+Unit",
    specifications: selectedSpecs,
    quantity: quantity,
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <h1 className="font-bebas text-5xl mb-4">INSULATED GLASS UNITS</h1>
            <p className="text-xl font-poppins max-w-3xl mx-auto">
              Premium sealed glass units engineered for maximum energy efficiency and performance. Customize your
              specifications and see real-time 3D visualization.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left Column - 3D Viewer */}
          <div className="space-y-6">
            {/* Enhanced 3D Viewer */}
            <div className="bg-gray-50 rounded-lg p-4 min-h-[750px]">
              <SealedGlass3DViewer
                width={selectedSpecs.width}
                height={selectedSpecs.height}
                thickness={selectedSpecs.thickness}
                coating={selectedSpecs.coating}
                spacer={selectedSpecs.spacer}
                gasFill={selectedSpecs.gasFill}
                grid={selectedSpecs.grid}
              />
            </div>

            {/* Key Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-primary-orange" />
                  Key Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4 text-green-600" />
                    <span className="text-sm">Energy Efficient</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4 text-blue-600" />
                    <span className="text-sm">UV Protection</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm">Sound Reduction</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Info className="w-4 h-4 text-purple-600" />
                    <span className="text-sm">Custom Sizes</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Configuration */}
          <div className="space-y-6">
            {/* Configuration Panel */}
            <Card>
              <CardHeader>
                <CardTitle>Configure Your Glass Unit</CardTitle>
                <CardDescription>Customize the specifications to meet your exact requirements</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Dimensions */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Dimensions</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Width: {selectedSpecs.width}"</Label>
                      <Slider
                        value={[selectedSpecs.width]}
                        onValueChange={(value) => setSelectedSpecs({ ...selectedSpecs, width: value[0] })}
                        min={12}
                        max={72}
                        step={1}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Height: {selectedSpecs.height}"</Label>
                      <Slider
                        value={[selectedSpecs.height]}
                        onValueChange={(value) => setSelectedSpecs({ ...selectedSpecs, height: value[0] })}
                        min={12}
                        max={96}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Thickness: {selectedSpecs.thickness}mm</Label>
                    <Slider
                      value={[selectedSpecs.thickness]}
                      onValueChange={(value) => setSelectedSpecs({ ...selectedSpecs, thickness: value[0] })}
                      min={12}
                      max={32}
                      step={2}
                      className="w-full"
                    />
                  </div>
                </div>

                <Separator />

                {/* Glass Coating */}
                <div className="space-y-3">
                  <Label>Glass Coating</Label>
                  <Select
                    value={selectedSpecs.coating}
                    onValueChange={(value) => setSelectedSpecs({ ...selectedSpecs, coating: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="clear">Clear Glass</SelectItem>
                      <SelectItem value="low-e">Low-E Coating (+$15)</SelectItem>
                      <SelectItem value="reflective">Reflective Coating (+$25)</SelectItem>
                      <SelectItem value="tinted">Tinted Glass (+$10)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Spacer Type */}
                <div className="space-y-3">
                  <Label>Spacer Type</Label>
                  <Select
                    value={selectedSpecs.spacer}
                    onValueChange={(value) => setSelectedSpecs({ ...selectedSpecs, spacer: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aluminum">Aluminum Spacer</SelectItem>
                      <SelectItem value="warm-edge">Warm Edge Spacer (+$8)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Gas Fill */}
                <div className="space-y-3">
                  <Label>Gas Fill</Label>
                  <Select
                    value={selectedSpecs.gasFill}
                    onValueChange={(value) => setSelectedSpecs({ ...selectedSpecs, gasFill: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="air">Air Fill</SelectItem>
                      <SelectItem value="argon">Argon Gas (+$12)</SelectItem>
                      <SelectItem value="krypton">Krypton Gas (+$35)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Grid Option */}
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Decorative Grid</Label>
                    <p className="text-sm text-gray-500">Add decorative grid pattern (+$20)</p>
                  </div>
                  <Switch
                    checked={selectedSpecs.grid}
                    onCheckedChange={(checked) => setSelectedSpecs({ ...selectedSpecs, grid: checked })}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Pricing and Add to Cart */}
            <Card>
              <CardHeader>
                <CardTitle>Pricing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Unit Price:</span>
                  <span className="font-semibold">${unitPrice.toFixed(2)}</span>
                </div>

                <div className="flex items-center gap-4">
                  <Label>Quantity:</Label>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={() => setQuantity(Math.max(1, quantity - 1))}>
                      -
                    </Button>
                    <span className="w-12 text-center">{quantity}</span>
                    <Button variant="outline" size="sm" onClick={() => setQuantity(quantity + 1)}>
                      +
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-between items-center text-lg font-semibold">
                  <span>Total Price:</span>
                  <span className="text-primary-orange">${totalPrice.toFixed(2)}</span>
                </div>

                <AddToCartButton product={productData} className="w-full bg-primary-orange hover:bg-orange-600">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Add to Cart
                </AddToCartButton>
              </CardContent>
            </Card>

            {/* Specifications Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Current Specifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="font-medium">Size:</span>
                    <br />
                    {selectedSpecs.width}" × {selectedSpecs.height}"
                  </div>
                  <div>
                    <span className="font-medium">Thickness:</span>
                    <br />
                    {selectedSpecs.thickness}mm
                  </div>
                  <div>
                    <span className="font-medium">Coating:</span>
                    <br />
                    {selectedSpecs.coating.charAt(0).toUpperCase() + selectedSpecs.coating.slice(1)}
                  </div>
                  <div>
                    <span className="font-medium">Spacer:</span>
                    <br />
                    {selectedSpecs.spacer.charAt(0).toUpperCase() + selectedSpecs.spacer.slice(1)}
                  </div>
                  <div>
                    <span className="font-medium">Gas Fill:</span>
                    <br />
                    {selectedSpecs.gasFill.charAt(0).toUpperCase() + selectedSpecs.gasFill.slice(1)}
                  </div>
                  <div>
                    <span className="font-medium">Grid:</span>
                    <br />
                    {selectedSpecs.grid ? "Yes" : "No"}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Additional Information Tabs */}
        <div className="mt-16">
          <Tabs defaultValue="specifications" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="installation">Installation</TabsTrigger>
              <TabsTrigger value="warranty">Warranty</TabsTrigger>
            </TabsList>

            <TabsContent value="specifications" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Technical Specifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-3">Glass Options</h4>
                      <ul className="space-y-2 text-sm">
                        <li>• Clear float glass (standard)</li>
                        <li>• Low-E coated glass available</li>
                        <li>• Reflective and tinted options</li>
                        <li>• Thickness: 3mm to 6mm per pane</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-3">Spacer Systems</h4>
                      <ul className="space-y-2 text-sm">
                        <li>• Aluminum spacer (standard)</li>
                        <li>• Warm edge spacer available</li>
                        <li>• Structural glazing compatible</li>
                        <li>• Desiccant filled for moisture control</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-orange">0.28</div>
                      <div className="text-sm text-gray-600">U-Value (W/m²K)</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-orange">0.70</div>
                      <div className="text-sm text-gray-600">Solar Heat Gain</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-orange">81%</div>
                      <div className="text-sm text-gray-600">Visible Light Transmission</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="installation" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Installation Guidelines</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Pre-Installation</h4>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>• Verify opening dimensions and squareness</li>
                        <li>• Check structural adequacy for glass weight</li>
                        <li>• Ensure proper drainage and weatherproofing</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Installation Process</h4>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>• Use appropriate glazing compounds and sealants</li>
                        <li>• Maintain proper edge clearances</li>
                        <li>• Follow manufacturer's torque specifications</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="warranty" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Warranty Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <Shield className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">10-Year Seal Warranty</h4>
                        <p className="text-sm text-gray-600">
                          Guaranteed against seal failure and fogging between glass panes
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Award className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">5-Year Manufacturing Warranty</h4>
                        <p className="text-sm text-gray-600">Coverage against defects in materials and workmanship</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Star className="w-5 h-5 text-yellow-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold">Lifetime Glass Breakage</h4>
                        <p className="text-sm text-gray-600">One-time replacement for accidental glass breakage</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
